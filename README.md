# PDF轉圖片轉換器

這個程序可以將PDFData資料夾內的PDF檔案轉換為圖片格式，並儲存到OutputImages資料夾。

## 檔案結構
```
MeasurePointParser/
├── PDFData/                    # PDF檔案資料夾
├── OutputImages/               # 輸出圖片資料夾 (自動建立)
├── pdf_to_image_converter.py   # 完整版轉換器 (互動式)
├── simple_pdf_converter.py     # 簡化版轉換器 (直接執行)
├── requirements.txt            # Python套件需求
├── run_converter.bat          # Windows批次執行檔
└── README.md                  # 說明文件
```

## 安裝需求

### 1. Python環境
- Python 3.7 或更新版本
- pip套件管理器

### 2. 必要套件
```bash
pip install -r requirements.txt
```

主要套件：
- `pdf2image`: PDF轉圖片核心庫
- `Pillow`: 圖片處理庫

### 3. 系統需求 (Windows)
pdf2image需要poppler工具，您可以：

**方法一：使用conda安裝 (推薦)**
```bash
conda install -c conda-forge poppler
```

**方法二：手動安裝**
1. 下載poppler for Windows: https://github.com/oschwartz10612/poppler-windows/releases/
2. 解壓縮到任意資料夾 (例如: `C:\poppler`)
3. 將 `C:\poppler\Library\bin` 加入系統PATH環境變數

## 使用方法

### 方法一：使用批次檔 (最簡單)
1. 雙擊執行 `run_converter.bat`
2. 程序會自動安裝套件並開始轉換

### 方法二：使用簡化版轉換器
```bash
python simple_pdf_converter.py
```

### 方法三：使用完整版轉換器 (更多選項)
```bash
python pdf_to_image_converter.py
```

完整版提供以下選項：
- 選擇輸出格式 (PNG/JPEG/TIFF)
- 調整DPI解析度
- 限制轉換檔案數量
- 測試模式 (只轉換前5個檔案)

## 輸出結果

轉換後的圖片會直接儲存在 `OutputImages` 資料夾中：

```
OutputImages/
├── 06BJ4001C001H_S_page_001.png
├── 06BJ4001C001H_S_page_002.png
├── 03C8U0110001TRB_S_page_001.png
├── 03C8U0110001TRB_S_page_002.png
├── 04CB50550001K_S_page_001.png
└── ...
```

所有圖片檔案都會直接放在OutputImages目錄下，檔名格式為：`PDF檔名_page_頁碼.副檔名`

## 設定說明

### DPI解析度
- **100 DPI**: 檔案較小，適合預覽
- **200 DPI**: 平衡品質與檔案大小 (預設)
- **300 DPI**: 高品質，檔案較大

### 圖片格式
- **PNG**: 無損壓縮，支援透明度，檔案較大
- **JPEG**: 有損壓縮，檔案較小，不支援透明度
- **TIFF**: 無損壓縮，高品質，檔案最大

## 故障排除

### 常見錯誤

1. **ModuleNotFoundError: No module named 'pdf2image'**
   - 解決：執行 `pip install pdf2image`

2. **PDFInfoNotInstalledError**
   - 解決：安裝poppler (參考上方系統需求)

3. **記憶體不足**
   - 解決：降低DPI或分批處理檔案

4. **權限錯誤**
   - 解決：確保對PDFData和OutputImages資料夾有讀寫權限

### 效能優化

- 對於大量檔案，建議先使用測試模式
- 可以調整DPI來平衡品質與處理速度
- 使用JPEG格式可以減少輸出檔案大小

## 日誌檔案

程序執行時會產生 `pdf_conversion.log` 檔案，記錄詳細的轉換過程和錯誤訊息。

## 技術支援

如果遇到問題，請檢查：
1. Python版本是否符合需求
2. 所有套件是否正確安裝
3. poppler是否正確安裝並加入PATH
4. PDF檔案是否損壞
5. 磁碟空間是否足夠 

# PCB量測點位解析器 - 批次處理版本

## 功能說明
此程式可以批次處理PDFData資料夾中的所有PDF檔案，自動解析PCB量測點位資料並輸出為CSV格式。

## 主要功能
1. **批次處理**: 自動處理PDFData資料夾中的所有PDF檔案
2. **智能解析**: 根據檔案名稱自動選擇適當的解析模板
3. **錯誤處理**: 完整的錯誤捕捉和記錄機制
4. **日誌記錄**: 詳細的執行日誌，包含成功和失敗記錄
5. **進度追蹤**: 即時顯示處理進度和統計資訊

## 使用方法

### 1. 準備工作
- 確保`PDFData`資料夾存在並包含要處理的PDF檔案
- 確保網路連線正常（需要連接到API伺服器）

### 2. 執行程式
```bash
python MeasurePointParser.py
```

### 3. 輸出結果
- **CSV檔案**: 每個PDF檔案會在同一目錄下生成對應的CSV檔案
- **日誌檔案**: 在`logs`資料夾中生成帶時間戳記的日誌檔案

## 輸出格式
生成的CSV檔案包含以下欄位：
- 管制類別
- 符號
- 尺寸
- 單位
- 原稿
- 工作片
- 目標值
- 客規下限
- 客規上限
- 客戶規格(+)
- 客戶規格(-)
- 客戶規格%(+)
- 客戶規格%(-)
- 量測位置
- 管制站別
- 管制目標值
- 管制下限
- 管制上限
- 製表者
- 日期
- 層別

## 日誌功能
程式會自動記錄以下資訊：
- 處理進度
- 成功/失敗狀態
- 錯誤訊息
- 統計資訊

日誌檔案位置：`logs/batch_process_YYYYMMDD_HHMMSS.log`

## 錯誤處理
程式具備完整的錯誤處理機制：
- API連線錯誤
- 檔案讀取錯誤
- CSV解析錯誤
- 檔案儲存錯誤

所有錯誤都會記錄在日誌中，並在最終報告中顯示失敗檔案清單。

## 注意事項
1. 確保API伺服器正常運作
2. 處理大量檔案時請注意網路穩定性
3. 程式會在每個檔案處理完後暫停2秒，避免API請求過於頻繁
4. 建議定期檢查日誌檔案以了解處理狀況

## 系統需求
- Python 3.6+
- pandas
- requests
- 網路連線 