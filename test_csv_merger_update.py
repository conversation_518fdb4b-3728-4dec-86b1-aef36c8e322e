#!/usr/bin/env python
# coding: utf-8

"""
測試CSV合併程式的PDF轉換功能
"""

from csv_merger import convert_csv_to_pdf_names, setup_logging

def test_csv_to_pdf_conversion():
    """測試CSV文件名稱轉PDF文件名稱功能"""
    print("測試CSV文件名稱轉PDF文件名稱功能")
    print("=" * 50)
    
    # 設定日誌
    logger = setup_logging()
    
    # 測試數據
    test_csv_list = "03C112570002TM_S.csv,03C112670001TR_S.csv,03C112670001T_S.csv,03C8U0110001TRB_S.csv,03CSN4650003R_S.csv,03CSN6250004T_S.csv,04CSN2150004T_S.csv,04CSN2780001_S.csv,04CSN2790001_S.csv,06B5P0190001V_S.csv"
    
    print("原始CSV文件清單:")
    print(test_csv_list)
    print()
    
    # 轉換為PDF文件名稱
    pdf_files = convert_csv_to_pdf_names(test_csv_list)
    
    print("轉換後的PDF文件清單:")
    print(pdf_files)
    print()
    
    # 記錄到日誌
    logger.info("測試CSV轉PDF功能")
    logger.info("跳過檔案清單(逗號分隔格式):")
    logger.info(test_csv_list)
    logger.info("對應的PDF檔案清單(逗號分隔格式):")
    logger.info(pdf_files)
    
    print("測試完成！請檢查日誌檔案中的輸出。")

if __name__ == "__main__":
    test_csv_to_pdf_conversion()
