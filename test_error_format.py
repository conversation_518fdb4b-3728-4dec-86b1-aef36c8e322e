#!/usr/bin/env python
# coding: utf-8

"""
測試失敗檔案清單格式輸出
"""

import os
from MeasurePointParser import setup_logging

def test_error_file_format():
    """測試失敗檔案清單的格式輸出"""
    print("測試失敗檔案清單格式輸出...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 模擬失敗檔案清單
    error_files = [
        'PDFData\\06BJ4001C001H_S.pdf',
        'PDFData\\03C112690001T_S.pdf',
        'PDFData\\04C8Q039B001TA_S.pdf'
    ]
    
    # 模擬原有的輸出格式
    logger.info("=" * 50)
    logger.info("批次處理完成")
    logger.info(f"總檔案數: 10")
    logger.info(f"成功處理: 7")
    logger.info(f"處理失敗: 3")
    logger.info(f"成功率: 70.0%")
    
    if error_files:
        logger.info("失敗檔案清單:")
        for error_file in error_files:
            logger.info(f"  - {error_file}")
        
        # 輸出逗號分隔格式，方便重新處理
        error_filenames = [os.path.basename(error_file) for error_file in error_files]
        logger.info("失敗檔案清單(逗號分隔格式):")
        logger.info(",".join(error_filenames))
    
    logger.info("=" * 50)
    
    print("測試完成！請檢查日誌檔案中的輸出格式。")
    print("現在失敗檔案清單會同時以兩種格式輸出：")
    print("1. 原有格式（每行一個檔案）")
    print("2. 逗號分隔格式（方便複製重新處理）")

if __name__ == "__main__":
    test_error_file_format()
