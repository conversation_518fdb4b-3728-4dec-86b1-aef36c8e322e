2025-06-24 18:14:02,772 - INFO - ==================================================
2025-06-24 18:14:02,772 - INFO - 開始處理指定的PDF檔案列表
2025-06-24 18:14:02,773 - INFO - ==================================================
2025-06-24 18:14:02,773 - INFO - 找到 1 個有效的PDF檔案
2025-06-24 18:14:02,774 - INFO -   - 04C8U0020004T_S.pdf
2025-06-24 18:14:02,774 - INFO - 處理進度: 1/1 - 04C8U0020004T_S.pdf
2025-06-24 18:14:02,774 - INFO - 開始處理檔案: PDFData\04C8U0020004T_S.pdf
2025-06-24 18:14:02,775 - INFO - 使用AI模型: gemini-2.5-pro (根據料號: C8U)
2025-06-24 18:14:02,775 - INFO - 步驟1: 解析點位資料 - 04C8U0020004T_S.pdf
2025-06-24 18:15:14,167 - INFO - API回應狀態碼: 200
2025-06-24 18:15:14,167 - INFO - API回應headers: {'Server': 'Werkzeug/3.1.3 Python/3.11.10', 'Date': 'Tue, 24 Jun 2025 10:15:37 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '18', 'Connection': 'close'}
2025-06-24 18:15:14,168 - INFO - API回應JSON結構: ['response']
2025-06-24 18:15:14,168 - INFO - ============================================================
2025-06-24 18:15:14,168 - INFO - 步驟1 - 模型回覆內容 (gemini-2.5-pro):
2025-06-24 18:15:14,169 - INFO - ============================================================
2025-06-24 18:15:14,169 - ERROR - API回應為None或不包含'response'欄位
2025-06-24 18:15:14,170 - INFO - 完整API回應: {"response":null}

2025-06-24 18:15:14,171 - INFO - ============================================================
2025-06-24 18:15:14,172 - ERROR - 解析API回應JSON時發生錯誤: API回應中沒有'response'欄位或其值為None
2025-06-24 18:15:14,172 - INFO - 原始API回應內容: {"response":null}

2025-06-24 18:15:14,173 - ERROR - 處理 PDFData\04C8U0020004T_S.pdf 時發生錯誤: API回應格式錯誤: API回應中沒有'response'欄位或其值為None
2025-06-24 18:15:14,175 - ERROR - ✗ 處理 PDFData\04C8U0020004T_S.pdf 時發生錯誤: API回應格式錯誤: API回應中沒有'response'欄位或其值為None
2025-06-24 18:15:14,180 - INFO - ==================================================
2025-06-24 18:15:14,181 - INFO - 指定檔案列表處理完成
2025-06-24 18:15:14,182 - INFO - 總檔案數: 1
2025-06-24 18:15:14,182 - INFO - 成功處理: 0
2025-06-24 18:15:14,183 - INFO - 處理失敗: 1
2025-06-24 18:15:14,184 - INFO - 成功率: 0.0%
2025-06-24 18:15:14,184 - INFO - 失敗檔案清單:
2025-06-24 18:15:14,185 - INFO -   - PDFData\04C8U0020004T_S.pdf
2025-06-24 18:15:14,186 - INFO - ==================================================
2025-06-24 18:17:07,497 - INFO - ==================================================
2025-06-24 18:17:07,497 - INFO - 開始處理指定的PDF檔案列表
2025-06-24 18:17:07,497 - INFO - ==================================================
2025-06-24 18:17:07,498 - INFO - 找到 1 個有效的PDF檔案
2025-06-24 18:17:07,499 - INFO -   - 04C8U0020004T_S.pdf
2025-06-24 18:17:07,499 - INFO - 處理進度: 1/1 - 04C8U0020004T_S.pdf
2025-06-24 18:17:07,500 - INFO - 開始處理檔案: PDFData\04C8U0020004T_S.pdf
2025-06-24 18:17:07,500 - INFO - 使用AI模型: gemini-2.5-pro (根據料號: C8U)
2025-06-24 18:17:07,504 - INFO - 步驟1: 解析點位資料 - 04C8U0020004T_S.pdf
2025-06-24 18:18:15,518 - INFO - API回應狀態碼: 200
2025-06-24 18:18:15,519 - INFO - API回應headers: {'Server': 'Werkzeug/3.1.3 Python/3.11.10', 'Date': 'Tue, 24 Jun 2025 10:18:38 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '1345', 'Connection': 'close'}
2025-06-24 18:18:15,520 - INFO - API回應JSON結構: ['response']
2025-06-24 18:18:15,521 - INFO - ============================================================
2025-06-24 18:18:15,521 - INFO - 步驟1 - 模型回覆內容 (gemini-2.5-pro):
2025-06-24 18:18:15,522 - INFO - ============================================================
2025-06-24 18:18:15,522 - INFO - API回應長度: 716 字元
2025-06-24 18:18:15,522 - INFO - 管制類別,符號,單位,原稿,工作片,目標值,客規下限,客規上限,客規規格(+),客規規格(-),規格類型(%or+-),尺寸,量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,寬度,下幅,蝕銅,9.15,8.20,10.10,汪素芬,2013/7/4
SMD,A,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,寬度,下幅,OQC,8.65,7.70,9.60,汪素芬,2013/7/4
SMD,A,MIL,7.10,10.90,7.10,5.90,10.10,3.0,-1.2,+/-,寬度,上幅,蝕銅,8.25,6.40,10.10,汪素芬,2013/7/4
SMD,A,MIL,7.10,10.90,7.10,5.90,10.10,3.0,-1.2,+/-,寬度,上幅,OQC,7.75,5.90,9.60,汪素芬,2013/7/4
SMD,A,MIL,10.8,8.8,10.8,9.6,12,1.2,-1.2,+/-,間距,間距,蝕銅,9.37,8.42,10.32,汪素芬,2013/7/4
SMD,A,MIL,10.8,8.8,10.8,9.6,12,1.2,-1.2,+/-,間距,間距,OQC,9.87,8.92,10.82,汪素芬,2013/7/4
SMD,B,MIL,27.60,29.60,27.60,26.40,28.80,1.2,-1.2,+/-,寬度,下幅,蝕銅,27.85,26.90,28.80,汪素芬,2013/7/4
SMD,B,MIL,27.60,29.6
2025-06-24 18:18:15,524 - INFO - ============================================================
2025-06-24 18:18:15,525 - INFO - 開始處理CSV回應資料
2025-06-24 18:18:15,525 - INFO - 原始回應長度: 716 字元
2025-06-24 18:18:15,526 - INFO - 清理後的CSV內容長度: 716 字元
2025-06-24 18:18:15,526 - INFO - 清理後的CSV內容預覽:
2025-06-24 18:18:15,527 - INFO - ----------------------------------------
2025-06-24 18:18:15,528 - INFO - 管制類別,符號,單位,原稿,工作片,目標值,客規下限,客規上限,客規規格(+),客規規格(-),規格類型(%or+-),尺寸,量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,寬度,下幅,蝕銅,9.15,8.20,10.10,汪素芬,2013/7/4
SMD,A,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,寬度,下幅,OQC,8.65,7.70,9.60,汪素芬,2013/7/4
SMD,A,MIL,7.10,10.90,7.10,5.90,10.10,3...
2025-06-24 18:18:15,529 - INFO - ----------------------------------------
2025-06-24 18:18:15,537 - INFO - 成功解析CSV，得到 8 行 19 列
2025-06-24 18:18:15,547 - INFO - 欄位名稱: ['管制類別', '符號', '單位', '原稿', '工作片', '目標值', '客規下限', '客規上限', '客規規格(+)', '客規規格(-)', '規格類型(%or+-)', '尺寸', '量測位置', '管制站別', '管制目標值', '管制下限', '管制上限', '製表者', '日期']
2025-06-24 18:18:15,549 - INFO - 最終DataFrame: 8 行 19 列
2025-06-24 18:18:15,550 - INFO - DataFrame前幾行預覽:
2025-06-24 18:18:15,617 - INFO -   管制類別 符號   單位    原稿   工作片   目標值  客規下限   客規上限 客規規格(+) 客規規格(-) 規格類型(%or+-)  尺寸 量測位置 管制站別 管制目標值  管制下限   管制上限  製表者        日期
0  SMD  A  MIL   8.9  10.9   8.9   7.7   10.1     1.2    -1.2         +/-  寬度   下幅   蝕銅  9.15  8.20  10.10  汪素芬  2013/7/4
1  SMD  A  MIL   8.9  10.9   8.9   7.7   10.1     1.2    -1.2         +/-  寬度   下幅  OQC  8.65  7.70   9.60  汪素芬  2013/7/4
2  SMD  A  MIL   7.1  10.9  7.10  5.90  10.10     3.0    -1.2         +/-  寬度   上幅   蝕銅  8.25  6.40  10.10  汪素芬  2013/7/4
3  SMD  A  MIL   7.1  10.9  7.10  5.90  10.10     3.0    -1.2         +/-  寬度   上幅  OQC  7.75  5.90   9.60  汪素芬  2013/7/4
4  SMD  A  MIL  10.8   8.8  10.8   9.6     12     1.2    -1.2         +/-  間距   間距   蝕銅  9.37  8.42  10.32  汪素芬  2013/7/4
2025-06-24 18:18:15,619 - INFO - 解析出 8 筆點位資料，符號種類: ['B', 'A']
2025-06-24 18:18:15,619 - INFO - 步驟2: 解析層別資料 - 04C8U0020004T_S.pdf
2025-06-24 18:18:35,295 - INFO - 步驟2 API回應狀態碼: 200
2025-06-24 18:18:35,296 - INFO - ============================================================
2025-06-24 18:18:35,297 - INFO - 步驟2 - 模型回覆內容 (gemini-2.5-pro):
2025-06-24 18:18:35,297 - INFO - ============================================================
2025-06-24 18:18:35,298 - INFO - 步驟2 API回應長度: 45 字元
2025-06-24 18:18:35,298 - INFO - ```csv
符號,層別
A,C面&S面
B,S面
C,C面&S面
D,C面&S面
```
2025-06-24 18:18:35,300 - INFO - ============================================================
2025-06-24 18:18:35,300 - INFO - 開始處理CSV回應資料
2025-06-24 18:18:35,300 - INFO - 原始回應長度: 45 字元
2025-06-24 18:18:35,301 - INFO - 移除了CSV標記前綴
2025-06-24 18:18:35,301 - INFO - 移除了CSV標記後綴
2025-06-24 18:18:35,303 - INFO - 清理後的CSV內容長度: 34 字元
2025-06-24 18:18:35,303 - INFO - 清理後的CSV內容預覽:
2025-06-24 18:18:35,304 - INFO - ----------------------------------------
2025-06-24 18:18:35,304 - INFO - 符號,層別
A,C面&S面
B,S面
C,C面&S面
D,C面&S面
2025-06-24 18:18:35,316 - INFO - ----------------------------------------
2025-06-24 18:18:35,319 - INFO - 成功解析CSV，得到 4 行 2 列
2025-06-24 18:18:35,319 - INFO - 欄位名稱: ['符號', '層別']
2025-06-24 18:18:35,320 - INFO - 最終DataFrame: 4 行 2 列
2025-06-24 18:18:35,321 - INFO - DataFrame前幾行預覽:
2025-06-24 18:18:35,327 - INFO -   符號     層別
0  A  C面&S面
1  B     S面
2  C  C面&S面
3  D  C面&S面
2025-06-24 18:18:35,331 - INFO - 解析出 4 筆層別資料
2025-06-24 18:18:35,333 - INFO - 合併後共 8 筆資料
2025-06-24 18:18:35,436 - INFO - 成功儲存至: PDFData\04C8U0020004T_S.csv
2025-06-24 18:18:35,436 - INFO - ✓ 成功處理 04C8U0020004T_S.pdf
2025-06-24 18:18:35,437 - INFO - ==================================================
2025-06-24 18:18:35,437 - INFO - 指定檔案列表處理完成
2025-06-24 18:18:35,437 - INFO - 總檔案數: 1
2025-06-24 18:18:35,438 - INFO - 成功處理: 1
2025-06-24 18:18:35,438 - INFO - 處理失敗: 0
2025-06-24 18:18:35,438 - INFO - 成功率: 100.0%
2025-06-24 18:18:35,438 - INFO - ==================================================
2025-06-24 18:36:34,330 - INFO - ==================================================
2025-06-24 18:36:34,330 - INFO - 開始處理指定的PDF檔案列表
2025-06-24 18:36:34,330 - INFO - ==================================================
2025-06-24 18:36:34,331 - INFO - 找到 1 個有效的PDF檔案
2025-06-24 18:36:34,331 - INFO -   - 04C8U0020004T_S.pdf
2025-06-24 18:36:34,332 - INFO - 處理進度: 1/1 - 04C8U0020004T_S.pdf
2025-06-24 18:36:34,332 - INFO - 開始處理檔案: PDFData\04C8U0020004T_S.pdf
2025-06-24 18:36:34,332 - INFO - 使用AI模型: gemini-2.5-pro (根據料號: C8U)
2025-06-24 18:36:34,332 - INFO - 步驟1: 解析點位資料 - 04C8U0020004T_S.pdf
2025-06-24 18:37:40,131 - INFO - API回應狀態碼: 200
2025-06-24 18:37:40,132 - INFO - API回應headers: {'Server': 'Werkzeug/3.1.3 Python/3.11.10', 'Date': 'Tue, 24 Jun 2025 10:38:03 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '867', 'Connection': 'close'}
2025-06-24 18:37:40,133 - INFO - API回應JSON結構: ['response']
2025-06-24 18:37:40,133 - INFO - ============================================================
2025-06-24 18:37:40,134 - INFO - 步驟1 - 模型回覆內容 (gemini-2.5-pro):
2025-06-24 18:37:40,134 - INFO - ============================================================
2025-06-24 18:37:40,135 - INFO - API回應長度: 392 字元
2025-06-24 18:37:40,135 - INFO - 管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,寬度,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,下幅,蝕銅,9.15,8.20,10.10,汪素芬,2013/7/4
SMD,A,寬度,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,下幅,OQC,8.65,7.70,9.60,汪素芬,2013/7/4
SMD,A,寬度,MIL,7.10,10.90,7.10,5.90,10.10,3.0,-1.2,+/-,上幅,蝕銅,8.25,6.40,10.10,汪素芬,2013/7/4
SMD,A,寬度,MIL,7.10,10.90,7.10,5.90,10.10,3.
2025-06-24 18:37:40,136 - INFO - ============================================================
2025-06-24 18:37:40,137 - INFO - 開始處理CSV回應資料
2025-06-24 18:37:40,137 - INFO - 原始回應長度: 392 字元
2025-06-24 18:37:40,137 - INFO - 清理後的CSV內容長度: 392 字元
2025-06-24 18:37:40,138 - INFO - 清理後的CSV內容預覽:
2025-06-24 18:37:40,138 - INFO - ----------------------------------------
2025-06-24 18:37:40,139 - INFO - 管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,寬度,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,下幅,蝕銅,9.15,8.20,10.10,汪素芬,2013/7/4
SMD,A,寬度,MIL,8.9,10.9,8.9,7.7,10.1,1.2,-1.2,+/-,下幅,OQC,8.65,7.70,9.60,汪素芬,2013/7/4
SMD,A,寬度,MIL,7.10,10.90,7.10,5.90,10.1...
2025-06-24 18:37:40,139 - INFO - ----------------------------------------
2025-06-24 18:37:40,144 - INFO - 成功解析CSV，得到 4 行 19 列
2025-06-24 18:37:40,144 - INFO - 欄位名稱: ['管制類別', '符號', '尺寸', '單位', '原稿', '工作片', '目標值', '客規下限', '客規上限', '客戶規格(+)', '客戶規格(-)', '規格類型(%or+-)', '量測位置', '管制站別', '管制目標值', '管制下限', '管制上限', '製表者', '日期']
2025-06-24 18:37:40,146 - INFO - 最終DataFrame: 4 行 19 列
2025-06-24 18:37:40,147 - INFO - DataFrame前幾行預覽:
2025-06-24 18:37:40,178 - INFO -   管制類別 符號  尺寸   單位   原稿   工作片  目標值  客規下限  客規上限  客戶規格(+) 客戶規格(-) 規格類型(%or+-) 量測位置 管制站別 管制目標值  管制下限   管制上限  製表者        日期
0  SMD  A  寬度  MIL  8.9  10.9  8.9   7.7  10.1      1.2    -1.2         +/-   下幅   蝕銅  9.15  8.20  10.10  汪素芬  2013/7/4
1  SMD  A  寬度  MIL  8.9  10.9  8.9   7.7  10.1      1.2    -1.2         +/-   下幅  OQC  8.65  7.70   9.60  汪素芬  2013/7/4
2  SMD  A  寬度  MIL  7.1  10.9  7.1   5.9  10.1      3.0    -1.2         +/-   上幅   蝕銅  8.25  6.40  10.10  汪素芬  2013/7/4
3  SMD  A  寬度  MIL  7.1  10.9  7.1   5.9  10.1      3.0                                                                
2025-06-24 18:37:40,179 - INFO - 解析出 4 筆點位資料，符號種類: ['A']
2025-06-24 18:37:40,179 - INFO - 步驟2: 解析層別資料 - 04C8U0020004T_S.pdf
2025-06-24 18:37:56,290 - INFO - 步驟2 API回應狀態碼: 200
2025-06-24 18:37:56,291 - INFO - ============================================================
2025-06-24 18:37:56,292 - INFO - 步驟2 - 模型回覆內容 (gemini-2.5-pro):
2025-06-24 18:37:56,293 - INFO - ============================================================
2025-06-24 18:37:56,293 - INFO - 步驟2 API回應長度: 42 字元
2025-06-24 18:37:56,294 - INFO - ```csv
符號,層別
A,C面&S面
B,S面
C,C面
D,C面&S面
```
2025-06-24 18:37:56,296 - INFO - ============================================================
2025-06-24 18:37:56,296 - INFO - 開始處理CSV回應資料
2025-06-24 18:37:56,298 - INFO - 原始回應長度: 42 字元
2025-06-24 18:37:56,298 - INFO - 移除了CSV標記前綴
2025-06-24 18:37:56,299 - INFO - 移除了CSV標記後綴
2025-06-24 18:37:56,300 - INFO - 清理後的CSV內容長度: 31 字元
2025-06-24 18:37:56,301 - INFO - 清理後的CSV內容預覽:
2025-06-24 18:37:56,301 - INFO - ----------------------------------------
2025-06-24 18:37:56,304 - INFO - 符號,層別
A,C面&S面
B,S面
C,C面
D,C面&S面
2025-06-24 18:37:56,308 - INFO - ----------------------------------------
2025-06-24 18:37:56,311 - INFO - 成功解析CSV，得到 4 行 2 列
2025-06-24 18:37:56,312 - INFO - 欄位名稱: ['符號', '層別']
2025-06-24 18:37:56,314 - INFO - 最終DataFrame: 4 行 2 列
2025-06-24 18:37:56,315 - INFO - DataFrame前幾行預覽:
2025-06-24 18:37:56,318 - INFO -   符號     層別
0  A  C面&S面
1  B     S面
2  C     C面
3  D  C面&S面
2025-06-24 18:37:56,318 - INFO - 解析出 4 筆層別資料
2025-06-24 18:37:56,322 - INFO - 合併後共 4 筆資料
2025-06-24 18:37:56,328 - INFO - 成功儲存至: PDFData\04C8U0020004T_S.csv
2025-06-24 18:37:56,329 - INFO - ✓ 成功處理 04C8U0020004T_S.pdf
2025-06-24 18:37:56,329 - INFO - ==================================================
2025-06-24 18:37:56,330 - INFO - 指定檔案列表處理完成
2025-06-24 18:37:56,330 - INFO - 總檔案數: 1
2025-06-24 18:37:56,330 - INFO - 成功處理: 1
2025-06-24 18:37:56,330 - INFO - 處理失敗: 0
2025-06-24 18:37:56,331 - INFO - 成功率: 100.0%
2025-06-24 18:37:56,331 - INFO - ==================================================
2025-06-24 19:01:31,519 - INFO - ==================================================
2025-06-24 19:01:31,519 - INFO - 開始批次處理PDF檔案
2025-06-24 19:01:31,519 - INFO - ==================================================
2025-06-24 19:01:31,522 - INFO - 找到 136 個PDF檔案
2025-06-24 19:01:31,522 - INFO - 處理進度: 1/136 - 03BC20100004R_S.pdf
2025-06-24 19:01:31,522 - INFO - 開始處理檔案: PDFData\03BC20100004R_S.pdf
2025-06-24 19:01:31,523 - INFO - 使用AI模型: gemini-2.5-flash (根據料號: BC2)
2025-06-24 19:01:31,523 - INFO - 步驟1: 解析點位資料 - 03BC20100004R_S.pdf
2025-06-24 19:02:04,665 - INFO - API回應狀態碼: 200
2025-06-24 19:02:04,666 - INFO - API回應headers: {'Server': 'Werkzeug/3.1.3 Python/3.11.10', 'Date': 'Tue, 24 Jun 2025 11:02:27 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '890', 'Connection': 'close'}
2025-06-24 19:02:04,668 - INFO - API回應JSON結構: ['response']
2025-06-24 19:02:04,669 - INFO - ============================================================
2025-06-24 19:02:04,670 - INFO - 步驟1 - 模型回覆內容 (gemini-2.5-flash):
2025-06-24 19:02:04,671 - INFO - ============================================================
2025-06-24 19:02:04,671 - INFO - API回應長度: 379 字元
2025-06-24 19:02:04,672 - INFO - ```csv
管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客規規格(+),客規規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,寬度,寬度,MIL,11.80,16.80,11.80,9.44,14.16,20,-20,%,量上幅,蝕銅,12.20,10.30,14.10,汪素芬,2023/6/27
SMD,寬度,寬度,MIL,11.80,16.80,11.80,9.44,14.16,20,-20,%,量上幅,粗化,12.00,10.10,13.90,汪素芬,2023/6/27
SMD,寬度,寬度,MIL,11.80,16.80,11.80,9.44,14.16,20,-20,%,量上幅,OQC,11.80,9.44,14.16,汪素芬,2023/6/27
```
2025-06-24 19:02:04,680 - INFO - ============================================================
2025-06-24 19:02:04,680 - INFO - 開始處理CSV回應資料
2025-06-24 19:02:04,681 - INFO - 原始回應長度: 379 字元
2025-06-24 19:02:04,682 - INFO - 移除了CSV標記前綴
2025-06-24 19:02:04,683 - INFO - 移除了CSV標記後綴
2025-06-24 19:02:04,684 - INFO - 清理後的CSV內容長度: 368 字元
2025-06-24 19:02:04,685 - INFO - 清理後的CSV內容預覽:
2025-06-24 19:02:04,685 - INFO - ----------------------------------------
2025-06-24 19:02:04,686 - INFO - 管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客規規格(+),客規規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,寬度,寬度,MIL,11.80,16.80,11.80,9.44,14.16,20,-20,%,量上幅,蝕銅,12.20,10.30,14.10,汪素芬,2023/6/27
SMD,寬度,寬度,MIL,11.80,16.80,11.80,9.44,14.16,20,-20,%,量上幅,粗化,12.00,10.10,13.90,汪素芬,2023/6/27
SMD,寬度,寬度,MIL,11.80,16...
2025-06-24 19:02:04,688 - INFO - ----------------------------------------
2025-06-24 19:02:04,693 - INFO - 成功解析CSV，得到 3 行 19 列
2025-06-24 19:02:04,695 - INFO - 欄位名稱: ['管制類別', '符號', '尺寸', '單位', '原稿', '工作片', '目標值', '客規下限', '客規上限', '客規規格(+)', '客規規格(-)', '規格類型(%or+-)', '量測位置', '管制站別', '管制目標值', '管制下限', '管制上限', '製表者', '日期']
2025-06-24 19:02:04,697 - INFO - 最終DataFrame: 3 行 19 列
2025-06-24 19:02:04,698 - INFO - DataFrame前幾行預覽:
2025-06-24 19:02:04,798 - INFO -   管制類別  符號  尺寸   單位    原稿   工作片   目標值  客規下限   客規上限  ...  客規規格(-)  規格類型(%or+-) 量測位置 管制站別 管制目標值   管制下限   管制上限  製表者         日期
0  SMD  寬度  寬度  MIL  11.8  16.8  11.8  9.44  14.16  ...      -20            %  量上幅   蝕銅  12.2  10.30  14.10  汪素芬  2023/6/27
1  SMD  寬度  寬度  MIL  11.8  16.8  11.8  9.44  14.16  ...      -20            %  量上幅   粗化  12.0  10.10  13.90  汪素芬  2023/6/27
2  SMD  寬度  寬度  MIL  11.8  16.8  11.8  9.44  14.16  ...      -20            %  量上幅  OQC  11.8   9.44  14.16  汪素芬  2023/6/27

[3 rows x 19 columns]
2025-06-24 19:02:04,829 - INFO - 解析出 3 筆點位資料，符號種類: ['寬度']
2025-06-24 19:02:04,836 - INFO - 步驟2: 解析層別資料 - 03BC20100004R_S.pdf
2025-06-24 19:02:27,645 - INFO - 步驟2 API回應狀態碼: 200
2025-06-24 19:02:27,646 - INFO - ============================================================
2025-06-24 19:02:27,646 - INFO - 步驟2 - 模型回覆內容 (gemini-2.5-flash):
2025-06-24 19:02:27,647 - INFO - ============================================================
2025-06-24 19:02:27,647 - INFO - 步驟2 API回應長度: 32 字元
2025-06-24 19:02:27,647 - INFO - ```csv
符號,層別
寬度,C面&S面
N/A,C面
```
2025-06-24 19:02:27,649 - INFO - ============================================================
2025-06-24 19:02:27,649 - INFO - 開始處理CSV回應資料
2025-06-24 19:02:27,649 - INFO - 原始回應長度: 32 字元
2025-06-24 19:02:27,650 - INFO - 移除了CSV標記前綴
2025-06-24 19:02:27,650 - INFO - 移除了CSV標記後綴
2025-06-24 19:02:27,650 - INFO - 清理後的CSV內容長度: 21 字元
2025-06-24 19:02:27,651 - INFO - 清理後的CSV內容預覽:
2025-06-24 19:02:27,652 - INFO - ----------------------------------------
2025-06-24 19:02:27,653 - INFO - 符號,層別
寬度,C面&S面
N/A,C面
2025-06-24 19:02:27,654 - INFO - ----------------------------------------
2025-06-24 19:02:27,659 - INFO - 成功解析CSV，得到 2 行 2 列
2025-06-24 19:02:27,659 - INFO - 欄位名稱: ['符號', '層別']
2025-06-24 19:02:27,661 - INFO - 最終DataFrame: 2 行 2 列
2025-06-24 19:02:27,661 - INFO - DataFrame前幾行預覽:
2025-06-24 19:02:27,671 - INFO -     符號     層別
0   寬度  C面&S面
1  N/A     C面
2025-06-24 19:02:27,686 - INFO - 解析出 2 筆層別資料
2025-06-24 19:02:27,908 - INFO - 合併後共 3 筆資料
2025-06-24 19:02:27,910 - INFO - 成功儲存至: PDFData\03BC20100004R_S.csv
2025-06-24 19:02:27,910 - INFO - ✓ 成功處理 03BC20100004R_S.pdf
2025-06-24 19:02:29,912 - INFO - 處理進度: 2/136 - 03C110890009R_S.pdf
2025-06-24 19:02:29,912 - INFO - 開始處理檔案: PDFData\03C110890009R_S.pdf
2025-06-24 19:02:29,913 - INFO - 使用AI模型: gemini-2.5-flash (根據料號: C11)
2025-06-24 19:02:29,914 - INFO - 步驟1: 解析點位資料 - 03C110890009R_S.pdf
2025-06-24 19:02:52,768 - INFO - API回應狀態碼: 200
2025-06-24 19:02:52,769 - INFO - API回應headers: {'Server': 'Werkzeug/3.1.3 Python/3.11.10', 'Date': 'Tue, 24 Jun 2025 11:03:15 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Content-Length': '1214', 'Connection': 'close'}
2025-06-24 19:02:52,769 - INFO - API回應JSON結構: ['response']
2025-06-24 19:02:52,770 - INFO - ============================================================
2025-06-24 19:02:52,770 - INFO - 步驟1 - 模型回覆內容 (gemini-2.5-flash):
2025-06-24 19:02:52,770 - INFO - ============================================================
2025-06-24 19:02:52,771 - INFO - API回應長度: 562 字元
2025-06-24 19:02:52,771 - INFO - 管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,無,寬度,MIL,19.00,20.00,19.00,18.00,20.00,1,-1,+/-,量下幅,蝕銅,19.35,18.50,20.20,邱美娟,2025/3/18
SMD,無,寬度,MIL,19.00,20.00,19.00,18.00,20.00,1,-1,+/-,量下幅,粗化,NA,NA,NA,邱美娟,2025/3/18
SMD,無,寬度,MIL,19.00,20.00,19.00,18.00,20.00,1,-1,+/-,量下幅,OQC,19.00,18.00,20.00,邱美娟,2025/3/18
SMD,無,間距,MIL,NA,NA,NA,NA,NA,1,-1,+/-,無規定,蝕銅,NA,NA,NA,邱美娟,2025/3/18
SMD,無,間距,MIL,NA,NA,NA,NA,NA,1,-1,+/-,無規定,粗化,NA,NA,NA,邱美娟,2025/3/18
SMD,無,間距,MIL,NA,NA,NA,NA,NA,1,-1,+/-,無規定,OQC,NA,NA,NA,邱美娟,2025/3/18
2025-06-24 19:02:52,772 - INFO - ============================================================
2025-06-24 19:02:52,773 - INFO - 開始處理CSV回應資料
2025-06-24 19:02:52,773 - INFO - 原始回應長度: 562 字元
2025-06-24 19:02:52,773 - INFO - 清理後的CSV內容長度: 562 字元
2025-06-24 19:02:52,774 - INFO - 清理後的CSV內容預覽:
2025-06-24 19:02:52,774 - INFO - ----------------------------------------
2025-06-24 19:02:52,775 - INFO - 管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,無,寬度,MIL,19.00,20.00,19.00,18.00,20.00,1,-1,+/-,量下幅,蝕銅,19.35,18.50,20.20,邱美娟,2025/3/18
SMD,無,寬度,MIL,19.00,20.00,19.00,18.00,20.00,1,-1,+/-,量下幅,粗化,NA,NA,NA,邱美娟,2025/3/18
SMD,無,寬度,MIL,19.00,20.00,19.00,...
2025-06-24 19:02:52,776 - INFO - ----------------------------------------
2025-06-24 19:02:52,778 - INFO - 成功解析CSV，得到 6 行 19 列
2025-06-24 19:02:52,778 - INFO - 欄位名稱: ['管制類別', '符號', '尺寸', '單位', '原稿', '工作片', '目標值', '客規下限', '客規上限', '客戶規格(+)', '客戶規格(-)', '規格類型(%or+-)', '量測位置', '管制站別', '管制目標值', '管制下限', '管制上限', '製表者', '日期']
2025-06-24 19:02:52,779 - INFO - 最終DataFrame: 6 行 19 列
2025-06-24 19:02:52,779 - INFO - DataFrame前幾行預覽:
2025-06-24 19:02:52,794 - INFO -   管制類別 符號  尺寸   單位     原稿    工作片    目標值   客規下限   客規上限  ...  客戶規格(-)  規格類型(%or+-) 量測位置 管制站別  管制目標值   管制下限   管制上限  製表者         日期
0  SMD  無  寬度  MIL  19.00  20.00  19.00  18.00  20.00  ...       -1          +/-  量下幅   蝕銅  19.35  18.50  20.20  邱美娟  2025/3/18
1  SMD  無  寬度  MIL  19.00  20.00  19.00  18.00  20.00  ...       -1          +/-  量下幅   粗化     NA     NA     NA  邱美娟  2025/3/18
2  SMD  無  寬度  MIL  19.00  20.00  19.00  18.00  20.00  ...       -1          +/-  量下幅  OQC  19.00  18.00  20.00  邱美娟  2025/3/18
3  SMD  無  間距  MIL     NA     NA     NA     NA     NA  ...       -1          +/-  無規定   蝕銅     NA     NA     NA  邱美娟  2025/3/18
4  SMD  無  間距  MIL     NA     NA     NA     NA     NA  ...       -1          +/-  無規定   粗化     NA     NA     NA  邱美娟  2025/3/18

[5 rows x 19 columns]
2025-06-24 19:02:52,795 - INFO - 解析出 6 筆點位資料，符號種類: ['無']
2025-06-24 19:02:52,796 - INFO - 步驟2: 解析層別資料 - 03C110890009R_S.pdf
