#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV資料解析示例
展示如何解析API回傳的CSV格式資料
"""

import pandas as pd
import io
import csv

# 模擬您的API回傳資料
csv_response = """MARK,層別
無,C面
無,S面"""

def parse_csv_with_pandas(csv_data):
    """使用pandas解析CSV資料"""
    print("=== 使用 pandas 解析 ===")
    
    # 將字串轉換為StringIO物件
    csv_io = io.StringIO(csv_data)
    
    # 讀取CSV資料
    df = pd.read_csv(csv_io)
    
    print("DataFrame:")
    print(df)
    print(f"\n資料形狀: {df.shape}")
    print(f"欄位名稱: {list(df.columns)}")
    
    # 存取特定資料
    print("\n=== 資料存取 ===")
    print("MARK欄位的所有值:")
    print(df['MARK'].tolist())
    
    print("\n層別欄位的所有值:")
    print(df['層別'].tolist())
    
    # 逐行處理
    print("\n=== 逐行處理 ===")
    for index, row in df.iterrows():
        print(f"第{index+1}行: MARK={row['MARK']}, 層別={row['層別']}")
    
    return df

def parse_csv_with_builtin(csv_data):
    """使用內建csv模組解析"""
    print("\n=== 使用內建 csv 模組解析 ===")
    
    # 將字串轉換為StringIO物件
    csv_io = io.StringIO(csv_data)
    
    # 建立CSV讀取器
    csv_reader = csv.DictReader(csv_io)
    
    # 讀取資料
    rows = list(csv_reader)
    
    print("解析結果:")
    for i, row in enumerate(rows, 1):
        print(f"第{i}行: {dict(row)}")
    
    return rows

def parse_csv_manually(csv_data):
    """手動解析CSV資料"""
    print("\n=== 手動解析 ===")
    
    lines = csv_data.strip().split('\n')
    
    # 取得標題行
    headers = lines[0].split(',')
    print(f"標題: {headers}")
    
    # 解析資料行
    data = []
    for line in lines[1:]:
        values = line.split(',')
        row_dict = dict(zip(headers, values))
        data.append(row_dict)
    
    print("解析結果:")
    for i, row in enumerate(data, 1):
        print(f"第{i}行: {row}")
    
    return data

def integrate_with_api_call():
    """整合到API呼叫中的示例"""
    print("\n=== 整合到API呼叫示例 ===")
    
    # 假設這是您的API呼叫函數
    def call_api():
        # 這裡應該是您的實際API呼叫
        # response = requests.post(...)
        # return response.text
        
        # 模擬回傳
        return """MARK,層別
無,C面
無,S面"""
    
    # 呼叫API並解析結果
    csv_response = call_api()
    
    # 使用pandas解析
    df = pd.read_csv(io.StringIO(csv_response))
    
    # 處理資料
    print("API回傳資料:")
    print(df)
    
    # 轉換為其他格式
    print("\n轉換為字典列表:")
    dict_list = df.to_dict('records')
    for item in dict_list:
        print(item)
    
    return df

if __name__ == "__main__":
    # 方法一：pandas
    df = parse_csv_with_pandas(csv_response)
    
    # 方法二：內建csv模組
    rows = parse_csv_with_builtin(csv_response)
    
    # 方法三：手動解析
    manual_data = parse_csv_manually(csv_response)
    
    # 整合示例
    integrate_with_api_call() 