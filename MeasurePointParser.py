#!/usr/bin/env python
# coding: utf-8

"""
PCB量測點位解析器 - 批次處理版本
功能：批次處理PDFData資料夾中的所有PDF檔案，解析量測點位資料並輸出CSV檔案
支援模式：
1. 批次處理所有PDF檔案
2. 處理指定的PDF檔案列表
"""

import requests
import base64
import json 
import os
import glob
import pandas as pd
import io
import logging
from datetime import datetime


def setup_logging():
    """設定日誌記錄"""
    # 建立logs資料夾
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 設定日誌檔案名稱（包含時間戳記）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/batch_process_{timestamp}.log'
    
    # 設定日誌格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()  # 同時輸出到控制台
        ]
    )
    
    return logging.getLogger(__name__)


def encode_file(file_path):
    """將檔案轉換為 base64 編碼"""
    with open(file_path, 'rb') as file:
        return base64.b64encode(file.read()).decode('utf-8')


def upload_files_with_message(file_paths, message="", model='gemini-2.0-flash'):
    """上傳多個檔案和文字訊息到伺服器"""
    url = 'http://172.16.2.25:5000/multimodalapi'
    
    # 準備檔案資料
    files_data = []
    for file_path in file_paths:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            content = encode_file(file_path)
            files_data.append({
                'filename': filename,
                'content': content
            })
    
    # 準備要傳送的資料
    payload = {
        'files': files_data,
        'message': message,
        'model': model
    }
    
    # 發送請求
    try:
        response = requests.post(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=300  # 5分鐘超時
        )
        
        if response.status_code != 200:
            raise Exception(f"HTTP錯誤: {response.status_code}")
        
        return response
        
    except Exception as e:
        raise Exception(f'API呼叫錯誤: {str(e)}')


def process_csv_response(api_response, logger=None):
    """處理API回應的CSV資料"""
    try:
        if logger:
            logger.info("開始處理CSV回應資料")
            logger.info(f"原始回應長度: {len(api_response)} 字元")

        # 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉
        lines = api_response.splitlines()
        if lines and (lines[0].strip() == '```csv' or lines[0].strip().lower() == 'csv'):
            csv_content = '\n'.join(lines[1:])
            if logger:
                logger.info("移除了CSV標記前綴")
        else:
            csv_content = api_response

        # 去掉最前後多餘的空白行，避免多讀到空列
        csv_content = csv_content.strip()

        # 如果字串末尾有單獨的 ```，去除它
        if csv_content.endswith('```'):
            csv_content = csv_content[:-3].rstrip()
            if logger:
                logger.info("移除了CSV標記後綴")

        if logger:
            logger.info(f"清理後的CSV內容長度: {len(csv_content)} 字元")
            logger.info("清理後的CSV內容預覽:")
            logger.info("-" * 40)
            logger.info(csv_content[:300] + ("..." if len(csv_content) > 300 else ""))
            logger.info("-" * 40)

        # 使用 io.StringIO 將 CSV 內容轉為類似檔案的物件
        csv_buffer = io.StringIO(csv_content)

        # 讀取 CSV 內容到 DataFrame，並把 'NA' 當作 NaN 處理
        # 加入更穩健的錯誤處理
        try:
            df = pd.read_csv(csv_buffer, keep_default_na=False)#, na_values=['NA'])
            if logger:
                logger.info(f"成功解析CSV，得到 {len(df)} 行 {len(df.columns)} 列")
                logger.info(f"欄位名稱: {list(df.columns)}")
        except Exception as parse_error:
            if logger:
                logger.warning(f"初次CSV解析失敗: {parse_error}")
                logger.info("嘗試使用寬鬆設定重新解析...")

            # 如果解析失敗，嘗試使用更寬鬆的設定
            csv_buffer = io.StringIO(csv_content)
            try:
                df = pd.read_csv(csv_buffer, keep_default_na=False, on_bad_lines='skip')
                if logger:
                    logger.info(f"寬鬆模式解析成功，得到 {len(df)} 行 {len(df.columns)} 列")
            except Exception:
                if logger:
                    logger.warning("寬鬆模式也失敗，嘗試手動清理CSV內容...")

                # 如果還是失敗，嘗試手動清理CSV內容
                cleaned_lines = []
                for line in csv_content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):  # 去除空行和註釋行
                        cleaned_lines.append(line)

                if len(cleaned_lines) > 0:
                    cleaned_csv = '\n'.join(cleaned_lines)
                    csv_buffer = io.StringIO(cleaned_csv)
                    df = pd.read_csv(csv_buffer, keep_default_na=False)
                    if logger:
                        logger.info(f"手動清理後解析成功，得到 {len(df)} 行 {len(df.columns)} 列")
                else:
                    raise Exception(f"無法解析CSV內容，原始錯誤: {parse_error}")

        # 如果還有全空的列，可再刪除：
        df = df.dropna(how='all')

        if logger:
            logger.info(f"最終DataFrame: {len(df)} 行 {len(df.columns)} 列")
            if len(df) > 0:
                logger.info("DataFrame前幾行預覽:")
                logger.info(str(df.head()))

        return df

    except Exception as e:
        # 加入更詳細的錯誤資訊用於調試
        error_msg = f"CSV處理錯誤: {str(e)}\n"
        error_msg += f"API回應內容:\n{api_response[:500]}..."  # 只顯示前500字元
        if logger:
            logger.error(error_msg)
        raise Exception(error_msg)


def get_message_template(filename):
    """根據檔案名稱取得對應的訊息模板"""
    if filename[2:5].upper() == 'C8U':
        return """您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：

管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28

請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，
轉換時請注意以下幾點：
1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期
2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析
3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析
4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)
5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同
6.若目標欄解析沒有值(NA)的項目不用輸出呈現
7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值
8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊
9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字"""
    else:
        return """您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：

管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28
SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28
光學點,B,寬度,MIL,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28
光學點,B,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28

請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，
轉換時請注意以下幾點：
1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期
2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析
3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析
4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)
5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同
6.若目標欄解析沒有值(NA)的項目不用輸出呈現
7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值
8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊
9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字"""


def get_layer_message_template(symbols):
    """取得層別解析的訊息模板"""
    return f"""您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：

符號,層別
A,C面&S面
B,S面
N/A,C面
NA,C面
,S面

請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，
轉換時請注意以下幾點：
1.此份PDF工單點位資料有定義{len(symbols)}種Mark點位：{'、'.join(symbols)}，請幫我解析他們被定義在C面還是S面還是兩面都有。
2.C、S面中的符號標示可能會轉90度
3.如圖片中有太小或太過於糊的符號請先放大增加解析度再解析結果
4.當Mark點位為N/A或空值時，C、S面就看哪一面有註記就算有
4.請直接輸出CSV格式表格，方便以python pandas讀取，不要有多餘的說明文字"""


def process_single_pdf(file_path, logger):
    """處理單一PDF檔案"""
    try:
        logger.info(f"開始處理檔案: {file_path}")
        
        # 判斷檔案類型並設定對應的訊息
        filename = os.path.basename(file_path)
        message = get_message_template(filename)
        
        # 根據料號(PDF文件名稱)2~5碼決定使用的AI模型
        model_name = 'gemini-2.5-pro' if filename[2:5].upper() == 'C8U' else 'gemini-2.5-flash'
        logger.info(f"使用AI模型: {model_name} (根據料號: {filename[2:5]})")
        
        # 第一步：解析點位資料
        logger.info(f"步驟1: 解析點位資料 - {filename}")
        files_to_upload = [file_path]
        response = upload_files_with_message(files_to_upload, message, model=model_name)

        api_response = response.json()['response']
        logger.info(f"API回應長度: {len(api_response)} 字元")

        # 記錄模型回覆內容到日誌
        logger.info("=" * 60)
        logger.info(f"步驟1 - 模型回覆內容 ({model_name}):")
        logger.info("=" * 60)
        logger.info(api_response)
        logger.info("=" * 60)

        # 處理第一個CSV回應
        df = process_csv_response(api_response)
        logger.info(f"解析出 {len(df)} 筆點位資料，符號種類: {list(set(df['符號']))}")

        # 第二步：解析層別資料
        logger.info(f"步驟2: 解析層別資料 - {filename}")
        symbols = list(set(df['符號']))
        mark_message = get_layer_message_template(symbols)

        response = upload_files_with_message(files_to_upload, mark_message, model=model_name)
        api_response_layer = response.json()['response']

        # 記錄第二步模型回覆內容到日誌
        logger.info("=" * 60)
        logger.info(f"步驟2 - 模型回覆內容 ({model_name}):")
        logger.info("=" * 60)
        logger.info(api_response_layer)
        logger.info("=" * 60)

        # 處理第二個CSV回應
        df_mark = process_csv_response(api_response_layer)
        logger.info(f"解析出 {len(df_mark)} 筆層別資料")
        
        # 合併資料
        df_merged = pd.merge(df, df_mark, on='符號', how='inner')
        logger.info(f"合併後共 {len(df_merged)} 筆資料")
        
        # 儲存結果
        output_path = file_path.replace('.pdf', '.csv')
        df_merged.to_csv(output_path, index=False, encoding='utf-8-sig')
        logger.info(f"成功儲存至: {output_path}")
        
        return True, f"成功處理 {filename}"
        
    except Exception as e:
        error_msg = f"處理 {file_path} 時發生錯誤: {str(e)}"
        logger.error(error_msg)
        return False, error_msg


def batch_process_pdfs():
    """批次處理所有PDF檔案"""
    # 設定日誌
    logger = setup_logging()
    logger.info("=" * 50)
    logger.info("開始批次處理PDF檔案")
    logger.info("=" * 50)
    
    # 取得所有PDF檔案
    files = glob.glob('PDFData/*.pdf')
    logger.info(f"找到 {len(files)} 個PDF檔案")
    
    if not files:
        logger.warning("PDFData資料夾中沒有找到PDF檔案")
        return
    
    # 統計變數
    success_count = 0
    error_count = 0
    error_files = []
    
    # 處理每個檔案
    for i, file_path in enumerate(files, 1):
        logger.info(f"處理進度: {i}/{len(files)} - {os.path.basename(file_path)}")
        
        success, message = process_single_pdf(file_path, logger)
        
        if success:
            success_count += 1
            logger.info(f"✓ {message}")
        else:
            error_count += 1
            error_files.append(file_path)
            logger.error(f"✗ {message}")
        
        # 在每個檔案處理完後稍作停頓，避免API請求過於頻繁
        if i < len(files):
            import time
            time.sleep(2)
    
    # 輸出最終統計
    logger.info("=" * 50)
    logger.info("批次處理完成")
    logger.info(f"總檔案數: {len(files)}")
    logger.info(f"成功處理: {success_count}")
    logger.info(f"處理失敗: {error_count}")
    logger.info(f"成功率: {success_count/len(files)*100:.1f}%")
    
    if error_files:
        logger.info("失敗檔案清單:")
        for error_file in error_files:
            logger.info(f"  - {error_file}")
    
    logger.info("=" * 50)
    
    # 回傳統計結果
    return {
        'total': len(files),
        'success': success_count,
        'error': error_count,
        'error_files': error_files
    }


def process_pdf_list(pdf_list):
    """處理指定的PDF檔案列表"""
    # 設定日誌
    logger = setup_logging()
    logger.info("=" * 50)
    logger.info("開始處理指定的PDF檔案列表")
    logger.info("=" * 50)
    
    # 驗證檔案存在並過濾有效檔案
    valid_files = []
    for pdf_file in pdf_list:
        # 如果是完整路徑就直接使用，否則加上PDFData/前綴
        if os.path.exists(pdf_file):
            file_path = pdf_file
        elif os.path.exists(os.path.join('PDFData', pdf_file)):
            file_path = os.path.join('PDFData', pdf_file)
        else:
            logger.warning(f"找不到檔案: {pdf_file}")
            continue
        
        if file_path.lower().endswith('.pdf'):
            valid_files.append(file_path)
        else:
            logger.warning(f"檔案格式不正確（非PDF）: {pdf_file}")
    
    if not valid_files:
        logger.warning("沒有找到有效的PDF檔案")
        return {
            'total': 0,
            'success': 0,
            'error': 0,
            'error_files': []
        }
    
    logger.info(f"找到 {len(valid_files)} 個有效的PDF檔案")
    for file_path in valid_files:
        logger.info(f"  - {os.path.basename(file_path)}")
    
    # 統計變數
    success_count = 0
    error_count = 0
    error_files = []
    
    # 處理每個檔案
    for i, file_path in enumerate(valid_files, 1):
        logger.info(f"處理進度: {i}/{len(valid_files)} - {os.path.basename(file_path)}")
        
        success, message = process_single_pdf(file_path, logger)
        
        if success:
            success_count += 1
            logger.info(f"✓ {message}")
        else:
            error_count += 1
            error_files.append(file_path)
            logger.error(f"✗ {message}")
        
        # 在每個檔案處理完後稍作停頓，避免API請求過於頻繁
        if i < len(valid_files):
            import time
            time.sleep(2)
    
    # 輸出最終統計
    logger.info("=" * 50)
    logger.info("指定檔案列表處理完成")
    logger.info(f"總檔案數: {len(valid_files)}")
    logger.info(f"成功處理: {success_count}")
    logger.info(f"處理失敗: {error_count}")
    logger.info(f"成功率: {success_count/len(valid_files)*100:.1f}%")
    
    if error_files:
        logger.info("失敗檔案清單:")
        for error_file in error_files:
            logger.info(f"  - {error_file}")
    
    logger.info("=" * 50)
    
    # 回傳統計結果
    return {
        'total': len(valid_files),
        'success': success_count,
        'error': error_count,
        'error_files': error_files
    }


def get_user_choice():
    """取得用戶選擇的處理模式"""
    print("\n請選擇處理模式：")
    print("1. 批次處理所有PDF檔案")
    print("2. 處理指定的PDF檔案列表")
    print("3. 退出程式")
    
    while True:
        try:
            choice = input("\n請輸入選項 (1-3): ").strip()
            if choice in ['1', '2', '3']:
                return int(choice)
            else:
                print("請輸入有效的選項 (1-3)")
        except KeyboardInterrupt:
            print("\n程式已取消")
            return 3
        except Exception:
            print("輸入錯誤，請重新輸入")


def get_pdf_list_from_user():
    """從用戶輸入取得PDF檔案列表"""
    print("\n請輸入要處理的PDF檔案名稱，可以使用以下方式：")
    print("1. 輸入檔案名稱（例如：file1.pdf）")
    print("2. 輸入多個檔案，用逗號分隔（例如：file1.pdf,file2.pdf,file3.pdf）")
    print("3. 輸入完整路徑（例如：PDFData/file1.pdf）")
    print("4. 輸入 'list' 查看可用的PDF檔案")
    
    while True:
        try:
            user_input = input("\n請輸入PDF檔案: ").strip()
            
            if user_input.lower() == 'list':
                # 顯示可用的PDF檔案
                pdf_files = glob.glob('PDFData/*.pdf')
                if pdf_files:
                    print("\n可用的PDF檔案：")
                    for i, file_path in enumerate(pdf_files, 1):
                        print(f"{i:2d}. {os.path.basename(file_path)}")
                else:
                    print("PDFData資料夾中沒有找到PDF檔案")
                continue
            
            if not user_input:
                print("請輸入檔案名稱")
                continue
            
            # 解析輸入的檔案列表
            pdf_list = [f.strip() for f in user_input.split(',') if f.strip()]
            
            if pdf_list:
                return pdf_list
            else:
                print("請輸入有效的檔案名稱")
                
        except KeyboardInterrupt:
            print("\n已取消輸入")
            return None
        except Exception as e:
            print(f"輸入錯誤: {e}")


def process_specific_pdfs(pdf_names):
    """
    便利函數：直接處理指定的PDF檔案列表（供程式調用）
    
    參數:
        pdf_names: PDF檔案名稱列表，例如 ['file1.pdf', 'file2.pdf']
                  可以是檔案名稱或完整路徑
    
    回傳:
        處理結果字典，包含統計資訊
    
    使用範例:
        # 處理單一檔案
        result = process_specific_pdfs(['03BC20100004R_S.pdf'])
        
        # 處理多個檔案
        result = process_specific_pdfs(['03BC20100004R_S.pdf', '04C110360003_S.pdf'])
    """
    return process_pdf_list(pdf_names)


if __name__ == "__main__":
    print("PCB量測點位解析器")
    print("=" * 30)
    
    while True:
        choice = get_user_choice()
        
        if choice == 1:
            # 批次處理所有PDF檔案
            print("\n開始批次處理所有PDF檔案...")
            result = batch_process_pdfs()
            
        elif choice == 2:
            # 處理指定的PDF檔案列表
            pdf_list = get_pdf_list_from_user()
            if pdf_list is None:
                continue
                
            print(f"\n開始處理指定的 {len(pdf_list)} 個PDF檔案...")
            result = process_pdf_list(pdf_list)
            
        elif choice == 3:
            # 退出程式
            print("程式已結束")
            break
        
        # 顯示處理結果
        if 'result' in locals() and result:
            print("\n" + "=" * 50)
            print("處理結果摘要")
            print("=" * 50)
            print(f"總檔案數: {result['total']}")
            print(f"成功處理: {result['success']}")
            print(f"處理失敗: {result['error']}")
            if result['total'] > 0:
                print(f"成功率: {result['success']/result['total']*100:.1f}%")
            
            if result['error_files']:
                print("\n失敗檔案:")
                for error_file in result['error_files']:
                    print(f"  - {os.path.basename(error_file)}")
            
            print("=" * 50)
        
        # 詢問是否繼續
        try:
            continue_choice = input("\n是否繼續使用程式？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("程式已結束")
                break
        except KeyboardInterrupt:
            print("\n程式已結束")
            break


# In[ ]:




