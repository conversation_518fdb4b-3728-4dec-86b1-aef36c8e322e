# API重試機制改進

## 修改概述

已成功為 `MeasurePointParser.py` 添加了智能重試機制，當遇到API調用失敗或回傳null時，程式會自動等待並重新嘗試，大幅提高成功率。

## 問題分析

根據您提供的日誌，主要問題包括：
1. **API回應為null**: `{"response":null}`
2. **HTTP 500錯誤**: 伺服器內部錯誤
3. **網路連接問題**: 暫時性的連接失敗

## 解決方案

### 1. 智能重試機制

```python
def upload_files_with_message(file_paths, message="", model='gemini-2.0-flash', 
                             max_retries=3, retry_delay=5, logger=None):
```

**重試參數：**
- `max_retries=3`: 最大重試次數（預設3次）
- `retry_delay=5`: 初始重試延遲（預設5秒）
- 每次重試延遲遞增（5秒 → 7秒 → 9秒）

### 2. 重試觸發條件

- **HTTP錯誤狀態碼**: 500, 502, 503, 504等
- **API回應為null**: `{"response": null}`
- **JSON解析錯誤**: 回應格式不正確
- **網路連接錯誤**: 超時、連接失敗等

### 3. 詳細日誌記錄

```
2025-06-24 19:30:00,123 - WARNING - API調用失敗 (嘗試 1/3): API回應中response欄位為null
2025-06-24 19:30:00,124 - INFO - 等待 5 秒後重試...
2025-06-24 19:30:05,125 - INFO - API重試第 2 次調用...
2025-06-24 19:30:10,126 - INFO - API重試成功！
```

## 使用方式

### 自動重試（預設設定）
程式會自動使用重試機制，無需額外設定：
- 最大重試次數：3次
- 初始延遲：5秒
- 延遲遞增：每次+2秒

### 自訂重試設定
如需調整重試參數，可修改函數調用：

```python
# 增加重試次數
response = upload_files_with_message(
    files_to_upload, 
    message, 
    model=model_name, 
    max_retries=5,      # 最大重試5次
    retry_delay=3,      # 初始延遲3秒
    logger=logger
)
```

## 重試流程

1. **第一次嘗試**: 正常API調用
2. **失敗檢測**: 檢查HTTP狀態碼和回應內容
3. **重試決策**: 如果未達最大重試次數，進入重試
4. **延遲等待**: 等待指定時間（遞增）
5. **重新嘗試**: 重新發送API請求
6. **成功/失敗**: 成功則返回，失敗則繼續重試或拋出異常

## 預期效果

### 改進前
```
成功率: 84.6%
失敗原因: API回應null、HTTP 500錯誤
```

### 改進後
```
預期成功率: 95%+ 
失敗大幅減少: 大部分暫時性錯誤會被重試機制解決
```

## 日誌輸出示例

### 成功重試的情況
```
2025-06-24 19:30:00,123 - INFO - 步驟1: 解析點位資料 - 03C112520004_S.pdf
2025-06-24 19:30:00,124 - WARNING - API調用失敗 (嘗試 1/3): HTTP錯誤: 500
2025-06-24 19:30:00,125 - INFO - 等待 5 秒後重試...
2025-06-24 19:30:05,126 - INFO - API重試第 2 次調用...
2025-06-24 19:30:10,127 - INFO - API重試成功！
2025-06-24 19:30:10,128 - INFO - API回應狀態碼: 200
```

### 最終失敗的情況
```
2025-06-24 19:30:00,123 - WARNING - API調用失敗 (嘗試 1/3): API回應中response欄位為null
2025-06-24 19:30:00,124 - INFO - 等待 5 秒後重試...
2025-06-24 19:30:05,125 - WARNING - API調用失敗 (嘗試 2/3): API回應中response欄位為null
2025-06-24 19:30:05,126 - INFO - 等待 7 秒後重試...
2025-06-24 19:30:12,127 - WARNING - API調用失敗 (嘗試 3/3): API回應中response欄位為null
2025-06-24 19:30:12,128 - ERROR - API調用最終失敗 (已嘗試 3 次): API回應中response欄位為null
```

## 技術特點

1. **漸進式延遲**: 避免對伺服器造成過大壓力
2. **詳細日誌**: 完整記錄重試過程
3. **智能檢測**: 檢查多種失敗情況
4. **向後兼容**: 不影響現有功能
5. **可配置**: 可根據需要調整重試參數

## 注意事項

- 重試會增加處理時間，但能顯著提高成功率
- 日誌檔案會記錄所有重試過程
- 如果API伺服器持續不穩定，建議聯繫伺服器管理員
- 可根據實際情況調整重試次數和延遲時間
