#!/usr/bin/env python
# coding: utf-8

"""
CSV文件整合程式
功能：合併PDFData目錄中的所有CSV文件，並在第一欄加入文件名稱
"""

import pandas as pd
import glob
import os
from datetime import datetime
import logging

def setup_logging():
    """設定日誌記錄"""
    # 建立logs資料夾
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # 設定日誌檔案名稱（包含時間戳記）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f'logs/csv_merger_{timestamp}.log'

    # 設定日誌格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()  # 同時輸出到控制台
        ]
    )

    return logging.getLogger(__name__)

def standardize_by_column_order(df):
    """
    按欄位順序標準化，不依賴表頭名稱

    參數:
        df: pandas DataFrame

    回傳:
        標準化後的DataFrame
    """
    # 定義標準表頭（20個欄位）
    standard_headers = [
        '管制類別', '符號', '尺寸', '單位', '原稿', '工作片', '目標值',
        '客規下限', '客規上限', '客戶規格(+)', '客戶規格(-)', '規格類型(%or+-)',
        '量測位置', '管制站別', '管制目標值', '管制下限', '管制上限',
        '製表者', '日期', '層別'
    ]

    # 獲取當前DataFrame的欄位數量
    current_columns = len(df.columns)

    # 如果欄位數量與標準不符，記錄警告但繼續處理
    if current_columns != len(standard_headers):
        print(f"警告：文件欄位數量 ({current_columns}) 與標準欄位數量 ({len(standard_headers)}) 不符")

    # 創建新的DataFrame，按順序重新命名欄位
    df_standardized = df.copy()

    # 根據實際欄位數量來重新命名
    if current_columns <= len(standard_headers):
        # 如果欄位數量不超過標準數量，直接按順序重新命名
        new_column_names = standard_headers[:current_columns]
        df_standardized.columns = new_column_names

        # 如果欄位數量少於標準數量，補充缺少的欄位
        if current_columns < len(standard_headers):
            for i in range(current_columns, len(standard_headers)):
                df_standardized[standard_headers[i]] = ''
    else:
        # 如果欄位數量超過標準數量，只保留前20個欄位
        df_standardized = df_standardized.iloc[:, :len(standard_headers)]
        df_standardized.columns = standard_headers

    # 確保最終的欄位順序正確
    df_final = df_standardized[standard_headers]

    return df_final

def merge_csv_files(input_dir='PDFData', output_file='merged_data.csv'):
    """
    合併指定目錄中的所有CSV文件
    
    參數:
        input_dir: 輸入目錄路徑
        output_file: 輸出文件名稱
    
    回傳:
        合併結果統計
    """
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("開始CSV文件整合作業")
    logger.info("=" * 60)
    
    # 尋找所有CSV文件
    csv_pattern = os.path.join(input_dir, '*.csv')
    csv_files = glob.glob(csv_pattern)
    
    if not csv_files:
        logger.warning(f"在 {input_dir} 目錄中沒有找到CSV文件")
        return {
            'total_files': 0,
            'success_files': 0,
            'error_files': 0,
            'total_rows': 0
        }
    
    logger.info(f"找到 {len(csv_files)} 個CSV文件")
    
    # 儲存所有數據的列表
    all_dataframes = []
    success_count = 0
    error_count = 0
    error_files = []
    skipped_files = []  # 新增：跳過的文件列表
    total_rows = 0
    
    # 處理每個CSV文件
    for i, csv_file in enumerate(csv_files, 1):
        try:
            filename = os.path.basename(csv_file)
            filename_without_ext = os.path.splitext(filename)[0]  # 去掉.csv副檔名
            
            logger.info(f"處理進度: {i}/{len(csv_files)} - {filename}")
            
            # 讀取CSV文件
            df = pd.read_csv(csv_file, encoding='utf-8-sig')

            # 檢查是否為空文件
            if df.empty:
                logger.warning(f"檔案 {filename} 是空的，跳過處理")
                skipped_files.append(csv_file)
                continue

            # 按欄位順序標準化，不依賴表頭名稱
            df = standardize_by_column_order(df)

            # 在第一欄插入文件名稱
            df.insert(0, '文件名稱', filename_without_ext)
            
            # 添加到列表中
            all_dataframes.append(df)
            success_count += 1
            total_rows += len(df)
            
            logger.info(f"✓ 成功處理 {filename}，包含 {len(df)} 筆資料")
            
        except Exception as e:
            error_count += 1
            error_files.append(csv_file)
            logger.error(f"✗ 處理 {filename} 時發生錯誤: {str(e)}")
    
    # 合併所有數據
    if all_dataframes:
        logger.info("開始合併所有數據...")
        
        try:
            # 合併所有DataFrame
            merged_df = pd.concat(all_dataframes, ignore_index=True)
            
            # 儲存合併後的文件
            merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            logger.info(f"合併完成！")
            logger.info(f"輸出文件: {output_file}")
            logger.info(f"總計 {len(merged_df)} 筆資料")
            logger.info(f"欄位: {list(merged_df.columns)}")
            
            # 顯示統計資訊
            logger.info("=" * 60)
            logger.info("整合統計結果")
            logger.info("=" * 60)
            logger.info(f"總文件數: {len(csv_files)}")
            logger.info(f"成功處理: {success_count}")
            logger.info(f"處理失敗: {error_count}")
            logger.info(f"跳過文件: {len(skipped_files)} (空文件)")
            logger.info(f"總資料筆數: {total_rows}")
            logger.info(f"成功率: {success_count/len(csv_files)*100:.1f}%")

            if error_files:
                logger.info("失敗檔案清單:")
                for error_file in error_files:
                    logger.info(f"  - {error_file}")

                # 輸出逗號分隔格式，方便重新處理
                error_filenames = [os.path.basename(error_file) for error_file in error_files]
                logger.info("失敗檔案清單(逗號分隔格式):")
                logger.info(",".join(error_filenames))

            if skipped_files:
                logger.info("跳過檔案清單 (空文件):")
                for skipped_file in skipped_files:
                    logger.info(f"  - {skipped_file}")

                # 輸出逗號分隔格式
                skipped_filenames = [os.path.basename(skipped_file) for skipped_file in skipped_files]
                logger.info("跳過檔案清單(逗號分隔格式):")
                logger.info(",".join(skipped_filenames))

                # 自動轉換為PDF文件名稱
                csv_names_string = ",".join(skipped_filenames)
                pdf_files = convert_csv_to_pdf_names(csv_names_string)
                logger.info("對應的PDF檔案清單(逗號分隔格式):")
                logger.info(pdf_files)
            
            logger.info("=" * 60)
            
            # 顯示合併後數據的預覽
            logger.info("合併後數據預覽（前5筆）:")
            logger.info(str(merged_df.head()))
            
            return {
                'total_files': len(csv_files),
                'success_files': success_count,
                'error_files': error_count,
                'skipped_files': len(skipped_files),
                'skipped_file_list': skipped_files,
                'error_file_list': error_files,
                'total_rows': len(merged_df),
                'output_file': output_file,
                'columns': list(merged_df.columns)
            }
            
        except Exception as e:
            logger.error(f"合併數據時發生錯誤: {str(e)}")
            return {
                'total_files': len(csv_files),
                'success_files': success_count,
                'error_files': error_count + 1,
                'total_rows': 0,
                'error': str(e)
            }
    else:
        logger.warning("沒有有效的數據可以合併")
        return {
            'total_files': len(csv_files),
            'success_files': 0,
            'error_files': len(csv_files),
            'total_rows': 0
        }

def merge_csv_with_custom_settings(input_dir='PDFData', output_file=None,
                                 include_pattern='*.csv', exclude_pattern=None):
    """
    進階CSV合併功能，支援自訂設定

    參數:
        input_dir: 輸入目錄
        output_file: 輸出文件名稱（如果為None，會自動生成）
        include_pattern: 包含的文件模式
        exclude_pattern: 排除的文件模式
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f'merged_csv_data_{timestamp}.csv'

    return merge_csv_files(input_dir, output_file)

def generate_summary_report(merged_file='merged_data.csv'):
    """
    生成合併數據的摘要報告

    參數:
        merged_file: 合併後的CSV文件路徑

    回傳:
        摘要統計資訊
    """
    logger = setup_logging()

    try:
        # 讀取合併後的數據
        df = pd.read_csv(merged_file, encoding='utf-8-sig')

        logger.info("=" * 60)
        logger.info("數據摘要報告")
        logger.info("=" * 60)

        # 基本統計
        logger.info(f"總資料筆數: {len(df)}")
        logger.info(f"總欄位數: {len(df.columns)}")
        logger.info(f"文件數量: {df['文件名稱'].nunique()}")

        # 欄位資訊
        logger.info(f"欄位清單: {list(df.columns)}")

        # 各文件的資料筆數統計
        file_counts = df['文件名稱'].value_counts()
        logger.info("各文件資料筆數統計:")
        for filename, count in file_counts.head(10).items():
            logger.info(f"  {filename}: {count} 筆")

        if len(file_counts) > 10:
            logger.info(f"  ... 還有 {len(file_counts) - 10} 個文件")

        # 管制類別統計
        if '管制類別' in df.columns:
            category_counts = df['管制類別'].value_counts()
            logger.info("管制類別統計:")
            for category, count in category_counts.items():
                logger.info(f"  {category}: {count} 筆")

        # 符號統計
        if '符號' in df.columns:
            symbol_counts = df['符號'].value_counts()
            logger.info("符號統計 (前10名):")
            for symbol, count in symbol_counts.head(10).items():
                logger.info(f"  {symbol}: {count} 筆")

        # 單位統計
        if '單位' in df.columns:
            unit_counts = df['單位'].value_counts()
            logger.info("單位統計:")
            for unit, count in unit_counts.items():
                logger.info(f"  {unit}: {count} 筆")

        # 層別統計
        if '層別' in df.columns:
            layer_counts = df['層別'].value_counts()
            logger.info("層別統計:")
            for layer, count in layer_counts.items():
                logger.info(f"  {layer}: {count} 筆")

        logger.info("=" * 60)

        return {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'unique_files': df['文件名稱'].nunique(),
            'file_counts': file_counts.to_dict(),
            'columns': list(df.columns)
        }

    except Exception as e:
        logger.error(f"生成摘要報告時發生錯誤: {str(e)}")
        return None

def export_by_category(merged_file='merged_data.csv', output_dir='分類輸出'):
    """
    按管制類別分別輸出CSV文件

    參數:
        merged_file: 合併後的CSV文件路徑
        output_dir: 輸出目錄
    """
    logger = setup_logging()

    try:
        # 建立輸出目錄
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 讀取合併後的數據
        df = pd.read_csv(merged_file, encoding='utf-8-sig')

        if '管制類別' not in df.columns:
            logger.warning("數據中沒有'管制類別'欄位，無法按類別分類")
            return

        # 按管制類別分組
        categories = df['管制類別'].unique()

        logger.info(f"開始按管制類別分別輸出，共 {len(categories)} 個類別")

        for category in categories:
            if pd.isna(category):
                category_name = "未分類"
                category_df = df[df['管制類別'].isna()]
            else:
                category_name = str(category)
                category_df = df[df['管制類別'] == category]

            # 輸出文件
            output_file = os.path.join(output_dir, f'{category_name}.csv')
            category_df.to_csv(output_file, index=False, encoding='utf-8-sig')

            logger.info(f"✓ 輸出 {category_name}: {len(category_df)} 筆資料 -> {output_file}")

        logger.info(f"按類別分類完成，文件已輸出至 {output_dir} 目錄")

    except Exception as e:
        logger.error(f"按類別分類時發生錯誤: {str(e)}")

def convert_csv_to_pdf_names(csv_names_string):
    """
    將逗號分隔的CSV文件名稱轉換為PDF文件名稱

    參數:
        csv_names_string: 逗號分隔的CSV文件名稱字串

    回傳:
        逗號分隔的PDF文件名稱字串
    """
    # 分割CSV文件名稱
    csv_files = [name.strip() for name in csv_names_string.split(',')]

    # 轉換為PDF文件名稱
    pdf_files = []
    for csv_file in csv_files:
        if csv_file.endswith('_S.csv'):
            # 移除 '_S.csv' 後綴，添加 '.pdf'
            pdf_name = csv_file.replace('_S.csv', '.pdf')
            pdf_files.append(pdf_name)
        elif csv_file.endswith('.csv'):
            # 如果只是 '.csv' 結尾，直接替換
            pdf_name = csv_file.replace('.csv', '.pdf')
            pdf_files.append(pdf_name)
        else:
            # 如果沒有副檔名，直接添加 '.pdf'
            pdf_files.append(csv_file + '.pdf')

    return ','.join(pdf_files)



if __name__ == "__main__":
    print("CSV文件整合程式")
    print("=" * 40)

    # 詢問用戶選擇
    print("\n請選擇操作模式：")
    print("1. 使用預設設定合併所有CSV文件")
    print("2. 自訂輸出文件名稱")
    print("3. 生成數據摘要報告")
    print("4. 按管制類別分別輸出")
    print("5. 退出程式")

    while True:
        try:
            choice = input("\n請輸入選項 (1-5): ").strip()

            if choice == '1':
                print("\n開始使用預設設定合併CSV文件...")
                result = merge_csv_files()
                break

            elif choice == '2':
                output_name = input("\n請輸入輸出文件名稱（例如：my_merged_data.csv）: ").strip()
                if not output_name:
                    output_name = 'merged_data.csv'
                if not output_name.endswith('.csv'):
                    output_name += '.csv'

                print(f"\n開始合併CSV文件，輸出至: {output_name}")
                result = merge_csv_files(output_file=output_name)
                break

            elif choice == '3':
                merged_file = input("\n請輸入要分析的合併文件名稱（預設：merged_data.csv）: ").strip()
                if not merged_file:
                    merged_file = 'merged_data.csv'

                if os.path.exists(merged_file):
                    print(f"\n開始生成 {merged_file} 的摘要報告...")
                    summary = generate_summary_report(merged_file)
                    if summary:
                        print("摘要報告已生成，請查看日誌檔案")
                    else:
                        print("摘要報告生成失敗")
                else:
                    print(f"文件 {merged_file} 不存在")
                break

            elif choice == '4':
                merged_file = input("\n請輸入要分類的合併文件名稱（預設：merged_data.csv）: ").strip()
                if not merged_file:
                    merged_file = 'merged_data.csv'

                output_dir = input("\n請輸入輸出目錄名稱（預設：分類輸出）: ").strip()
                if not output_dir:
                    output_dir = '分類輸出'

                if os.path.exists(merged_file):
                    print(f"\n開始按管制類別分類 {merged_file}...")
                    export_by_category(merged_file, output_dir)
                    print(f"分類完成，文件已輸出至 {output_dir} 目錄")
                else:
                    print(f"文件 {merged_file} 不存在")
                break

            elif choice == '5':
                print("程式已結束")
                exit()

            else:
                print("請輸入有效的選項 (1-5)")

        except KeyboardInterrupt:
            print("\n程式已取消")
            exit()
        except Exception as e:
            print(f"輸入錯誤: {e}")

    # 顯示結果
    if 'result' in locals() and result:
        print("\n" + "=" * 50)
        print("整合結果摘要")
        print("=" * 50)
        print(f"總文件數: {result['total_files']}")
        print(f"成功處理: {result['success_files']}")
        print(f"處理失敗: {result['error_files']}")
        if 'skipped_files' in result:
            print(f"跳過文件: {result['skipped_files']} (空文件)")
        print(f"總資料筆數: {result['total_rows']}")
        if result['total_files'] > 0:
            print(f"成功率: {result['success_files']/result['total_files']*100:.1f}%")

        if 'output_file' in result:
            print(f"輸出文件: {result['output_file']}")

        # 顯示失敗和跳過的文件詳細資訊
        if 'error_file_list' in result and result['error_file_list']:
            print(f"\n處理失敗的文件 ({len(result['error_file_list'])} 個):")
            for error_file in result['error_file_list']:
                print(f"  - {os.path.basename(error_file)}")

            # 逗號分隔格式
            error_filenames = [os.path.basename(f) for f in result['error_file_list']]
            print(f"\n失敗檔案清單(逗號分隔格式):")
            print(",".join(error_filenames))

        if 'skipped_file_list' in result and result['skipped_file_list']:
            print(f"\n跳過的文件 ({len(result['skipped_file_list'])} 個空文件):")
            for skipped_file in result['skipped_file_list']:
                print(f"  - {os.path.basename(skipped_file)}")

            # 逗號分隔格式
            skipped_filenames = [os.path.basename(f) for f in result['skipped_file_list']]
            print(f"\n跳過檔案清單(逗號分隔格式):")
            print(",".join(skipped_filenames))

            # 自動轉換並顯示對應的PDF文件清單
            csv_names_string = ",".join(skipped_filenames)
            pdf_files = convert_csv_to_pdf_names(csv_names_string)
            print(f"\n對應的PDF檔案清單(逗號分隔格式):")
            print(pdf_files)

        print("=" * 50)

        # 詢問是否要生成摘要報告
        if 'output_file' in result:
            generate_report = input(f"\n是否要生成 {result['output_file']} 的摘要報告？(y/n): ").strip().lower()
            if generate_report in ['y', 'yes', '是']:
                print("正在生成摘要報告...")
                summary = generate_summary_report(result['output_file'])
                if summary:
                    print("摘要報告已生成，請查看日誌檔案")

            # 詢問是否要按類別分類
            export_categories = input(f"\n是否要按管制類別分別輸出？(y/n): ").strip().lower()
            if export_categories in ['y', 'yes', '是']:
                print("正在按管制類別分類...")
                export_by_category(result['output_file'])
                print("分類完成")
