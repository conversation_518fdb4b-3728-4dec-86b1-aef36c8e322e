#!/usr/bin/env python
# coding: utf-8

"""
測試按欄位順序合併CSV文件的功能
"""

import pandas as pd
from csv_merger import standardize_by_column_order, setup_logging

def test_column_order_standardization():
    """測試按欄位順序標準化功能"""
    print("測試按欄位順序標準化功能")
    print("=" * 50)
    
    # 設定日誌
    logger = setup_logging()
    
    # 測試不同的CSV文件結構
    test_files = [
        'PDFData/03BC20100004R_S.csv',
        'PDFData/03C110890009R_S.csv',
        'PDFData/03C111310003R_S.csv'
    ]
    
    for i, test_file in enumerate(test_files, 1):
        try:
            print(f"\n測試文件 {i}: {test_file}")
            
            # 讀取原始文件
            df_original = pd.read_csv(test_file, encoding='utf-8-sig')
            print(f"原始欄位數量: {len(df_original.columns)}")
            print(f"原始欄位名稱: {list(df_original.columns)}")
            
            # 標準化處理
            df_standardized = standardize_by_column_order(df_original)
            print(f"標準化後欄位數量: {len(df_standardized.columns)}")
            print(f"標準化後欄位名稱: {list(df_standardized.columns)}")
            
            # 記錄到日誌
            logger.info(f"測試文件: {test_file}")
            logger.info(f"原始欄位: {list(df_original.columns)}")
            logger.info(f"標準化後欄位: {list(df_standardized.columns)}")
            
        except Exception as e:
            print(f"處理文件 {test_file} 時發生錯誤: {str(e)}")
            logger.error(f"處理文件 {test_file} 時發生錯誤: {str(e)}")
    
    print("\n" + "=" * 50)
    print("測試完成！")

def test_small_merge():
    """測試小規模合併"""
    print("\n測試小規模合併功能")
    print("=" * 50)
    
    from csv_merger import merge_csv_files
    
    # 創建測試目錄（如果不存在）
    import os
    if not os.path.exists('test_data'):
        os.makedirs('test_data')
    
    # 創建測試CSV文件
    test_data1 = {
        '管制類別': ['SMD', 'SMD'],
        '符號': ['A', 'B'],
        '尺寸': ['寬度', '長度'],
        '單位': ['MIL', 'MIL'],
        '原稿': [12.0, 15.0],
        '工作片': [13.0, 16.0],
        '目標值': [12.5, 15.5],
        '客規下限': [11.0, 14.0],
        '客規上限': [14.0, 17.0],
        '客戶規格(+)': [1, 1],
        '客戶規格(-)': [-1, -1],
        '規格類型(%or+-)': ['+/-', '+/-'],
        '量測位置': ['位置1', '位置2'],
        '管制站別': ['蝕銅', 'OQC'],
        '管制目標值': [12.3, 15.3],
        '管制下限': [11.5, 14.5],
        '管制上限': [13.5, 16.5],
        '製表者': ['測試者', '測試者'],
        '日期': ['2025/6/25', '2025/6/25'],
        '層別': ['S面', 'C面']
    }
    
    test_data2 = {
        '管制類別': ['SMD'],
        '符號': ['C'],
        '尺寸': ['厚度'],
        '單位': ['MIL'],
        '原稿': [8.0],
        '工作片': [9.0],
        '目標值': [8.5],
        '客規下限': [7.0],
        '客規上限': [10.0],
        '客戶規格(+)': [1],
        '客戶規格(-)': [-1],
        '規格類型(%or+-)': ['+/-'],
        '量測位置': ['位置3'],
        '管制站別': ['粗化'],
        '管制目標值': [8.3],
        '管制下限': [7.5],
        '管制上限': [9.5],
        '製表者': ['測試者'],
        '日期': ['2025/6/25'],
        '層別': ['S面']
    }
    
    # 儲存測試文件
    pd.DataFrame(test_data1).to_csv('test_data/test1.csv', index=False, encoding='utf-8-sig')
    pd.DataFrame(test_data2).to_csv('test_data/test2.csv', index=False, encoding='utf-8-sig')
    
    print("已創建測試文件")
    print("測試文件將使用標準化的欄位順序進行合併")

if __name__ == "__main__":
    test_column_order_standardization()
    test_small_merge()
