# 失敗檔案清單格式改進

## 修改概述

已成功為 `MeasurePointParser.py` 添加了逗號分隔格式的失敗檔案清單輸出，方便您直接複製使用來重新處理失敗的檔案。

## 修改內容

### 原有格式（保持不變）
```
失敗檔案清單:
  - PDFData\06BJ4001C001H_S.pdf
  - PDFData\03C112690001T_S.pdf
  - PDFData\04C8Q039B001TA_S.pdf
```

### 新增格式（逗號分隔）
```
失敗檔案清單(逗號分隔格式):
06BJ4001C001H_S.pdf,03C112690001T_S.pdf,04C8Q039B001TA_S.pdf
```

## 使用方式

1. **運行程式**：正常執行批次處理或指定檔案處理
2. **查看日誌**：在日誌檔案中找到失敗檔案清單
3. **複製格式**：直接複製逗號分隔格式的檔案名稱
4. **重新處理**：將複製的內容貼到程式的檔案輸入欄位中

## 實際使用範例

### 步驟1：查看日誌輸出
```
2025-06-24 19:12:22,030 - INFO - 失敗檔案清單:
2025-06-24 19:12:22,031 - INFO -   - PDFData\06BJ4001C001H_S.pdf
2025-06-24 19:12:22,031 - INFO -   - PDFData\03C112690001T_S.pdf
2025-06-24 19:12:22,031 - INFO -   - PDFData\04C8Q039B001TA_S.pdf
2025-06-24 19:12:22,031 - INFO - 失敗檔案清單(逗號分隔格式):
2025-06-24 19:12:22,032 - INFO - 06BJ4001C001H_S.pdf,03C112690001T_S.pdf,04C8Q039B001TA_S.pdf
```

### 步驟2：複製逗號分隔格式
複製這行內容：
```
06BJ4001C001H_S.pdf,03C112690001T_S.pdf,04C8Q039B001TA_S.pdf
```

### 步驟3：重新處理
1. 運行程式
2. 選擇 "2. 處理指定的PDF檔案列表"
3. 貼上複製的內容：`06BJ4001C001H_S.pdf,03C112690001T_S.pdf,04C8Q039B001TA_S.pdf`
4. 程式會自動解析並重新處理這些失敗的檔案

## 修改的函數

1. **`batch_process_pdfs()`**：批次處理所有PDF檔案
2. **`process_pdf_list()`**：處理指定的PDF檔案列表

兩個函數都已添加相同的逗號分隔格式輸出功能。

## 優點

1. **方便重新處理**：直接複製貼上，無需手動輸入檔案名稱
2. **減少錯誤**：避免手動輸入時的拼寫錯誤
3. **提高效率**：快速重新處理失敗的檔案
4. **向後兼容**：保持原有的詳細清單格式
5. **符合用戶記憶**：根據您提到的記憶偏好，使用逗號分隔格式

## 注意事項

- 逗號分隔格式只包含檔案名稱（不含路徑），程式會自動在 PDFData 資料夾中尋找
- 原有的詳細格式仍然保留，提供完整的路徑資訊
- 兩種格式都會出現在日誌中，您可以根據需要選擇使用
