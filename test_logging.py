#!/usr/bin/env python
# coding: utf-8

"""
測試日誌記錄功能
"""

import logging
from datetime import datetime
import os

# 導入主程式的函數
from MeasurePointParser import setup_logging, process_csv_response

def test_logging_setup():
    """測試日誌設定功能"""
    print("測試日誌設定功能...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 測試各種日誌級別
    logger.info("這是一個資訊訊息")
    logger.warning("這是一個警告訊息")
    logger.error("這是一個錯誤訊息")
    
    # 測試模型回覆記錄格式
    logger.info("=" * 60)
    logger.info("測試 - 模型回覆內容 (gemini-2.5-flash):")
    logger.info("=" * 60)
    logger.info("這是模擬的模型回覆內容\n包含多行文字\n和CSV格式資料")
    logger.info("=" * 60)
    
    print("日誌測試完成，請檢查 logs 資料夾中的日誌檔案")

def test_csv_processing_with_logging():
    """測試CSV處理功能的日誌記錄"""
    print("測試CSV處理功能的日誌記錄...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 模擬API回應
    mock_api_response = """```csv
管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28
SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28
```"""
    
    try:
        # 測試CSV處理功能
        df = process_csv_response(mock_api_response, logger)
        logger.info(f"測試成功：處理了 {len(df)} 筆資料")
        print(f"CSV處理測試成功：得到 {len(df)} 筆資料")
        
    except Exception as e:
        logger.error(f"CSV處理測試失敗: {str(e)}")
        print(f"CSV處理測試失敗: {str(e)}")

def test_error_logging():
    """測試錯誤日誌記錄"""
    print("測試錯誤日誌記錄...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 模擬錯誤的API回應
    invalid_csv = "這不是有效的CSV格式"
    
    try:
        df = process_csv_response(invalid_csv, logger)
    except Exception as e:
        logger.error(f"預期的錯誤測試: {str(e)}")
        print("錯誤日誌記錄測試完成")

if __name__ == "__main__":
    print("開始測試日誌記錄功能")
    print("=" * 50)
    
    # 測試1: 基本日誌設定
    test_logging_setup()
    print()
    
    # 測試2: CSV處理日誌
    test_csv_processing_with_logging()
    print()
    
    # 測試3: 錯誤日誌
    test_error_logging()
    print()
    
    print("=" * 50)
    print("所有測試完成！")
    print("請檢查 logs 資料夾中的日誌檔案，確認模型回覆內容是否正確記錄。")
