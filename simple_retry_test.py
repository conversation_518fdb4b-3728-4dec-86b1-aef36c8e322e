#!/usr/bin/env python
# coding: utf-8

"""
簡單的重試機制測試
"""

from MeasurePointParser import setup_logging

def test_retry_logging():
    """測試重試機制的日誌記錄"""
    print("測試重試機制的日誌記錄...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 模擬重試過程的日誌記錄
    logger.info("=" * 50)
    logger.info("開始測試重試機制")
    logger.info("=" * 50)
    
    # 模擬第一次API調用失敗
    logger.info("步驟1: 解析點位資料 - test_file.pdf")
    logger.warning("API調用失敗 (嘗試 1/3): API回應中response欄位為null")
    logger.info("等待 5 秒後重試...")
    
    # 模擬第二次API調用失敗
    logger.info("API重試第 2 次調用...")
    logger.warning("API調用失敗 (嘗試 2/3): HTTP錯誤: 500")
    logger.info("等待 7 秒後重試...")
    
    # 模擬第三次API調用成功
    logger.info("API重試第 3 次調用...")
    logger.info("API重試成功！")
    logger.info("API回應狀態碼: 200")
    
    logger.info("=" * 50)
    logger.info("重試機制測試完成")
    logger.info("=" * 50)
    
    print("重試機制日誌記錄測試完成！")
    print("請檢查 logs 資料夾中的日誌檔案。")

if __name__ == "__main__":
    test_retry_logging()
