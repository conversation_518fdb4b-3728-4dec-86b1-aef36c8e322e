2025-06-24 18:06:57,375 - INFO - ==================================================
2025-06-24 18:06:57,375 - INFO - 開始處理指定的PDF檔案列表
2025-06-24 18:06:57,375 - INFO - ==================================================
2025-06-24 18:06:57,376 - INFO - 找到 1 個有效的PDF檔案
2025-06-24 18:06:57,376 - INFO -   - 04C8U0020004T_S.pdf
2025-06-24 18:06:57,377 - INFO - 處理進度: 1/1 - 04C8U0020004T_S.pdf
2025-06-24 18:06:57,377 - INFO - 開始處理檔案: PDFData\04C8U0020004T_S.pdf
2025-06-24 18:06:57,378 - INFO - 使用AI模型: gemini-2.5-pro (根據料號: C8U)
2025-06-24 18:06:57,378 - INFO - 步驟1: 解析點位資料 - 04C8U0020004T_S.pdf
2025-06-24 18:08:10,454 - ERROR - 處理 PDFData\04C8U0020004T_S.pdf 時發生錯誤: object of type 'NoneType' has no len()
2025-06-24 18:08:10,454 - ERROR - ✗ 處理 PDFData\04C8U0020004T_S.pdf 時發生錯誤: object of type 'NoneType' has no len()
2025-06-24 18:08:10,455 - INFO - ==================================================
2025-06-24 18:08:10,455 - INFO - 指定檔案列表處理完成
2025-06-24 18:08:10,456 - INFO - 總檔案數: 1
2025-06-24 18:08:10,456 - INFO - 成功處理: 0
2025-06-24 18:08:10,457 - INFO - 處理失敗: 1
2025-06-24 18:08:10,457 - INFO - 成功率: 0.0%
2025-06-24 18:08:10,458 - INFO - 失敗檔案清單:
2025-06-24 18:08:10,458 - INFO -   - PDFData\04C8U0020004T_S.pdf
2025-06-24 18:08:10,458 - INFO - ==================================================
