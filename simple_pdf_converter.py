#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版PDF轉圖片轉換器
直接執行即可將PDFData內的所有PDF轉換為PNG圖片
"""

import os
from pathlib import Path
from pdf2image import convert_from_path

def convert_pdfs_to_images():
    """將PDFData資料夾內的PDF轉換為圖片"""
    
    # 設定路徑
    pdf_folder = Path("PDFData")
    output_folder = Path("OutputImages")
    
    # 建立輸出資料夾
    output_folder.mkdir(exist_ok=True)
    
    # 檢查PDF資料夾是否存在
    if not pdf_folder.exists():
        print(f"❌ 找不到PDF資料夾: {pdf_folder}")
        return
    
    # 取得所有PDF檔案
    pdf_files = list(pdf_folder.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到任何PDF檔案")
        return
    
    print(f"📁 找到 {len(pdf_files)} 個PDF檔案")
    print("🔄 開始轉換...")
    
    success_count = 0
    
    for i, pdf_file in enumerate(pdf_files, 1):
        try:
            print(f"[{i}/{len(pdf_files)}] 轉換中: {pdf_file.name}")
            
            # 轉換PDF為圖片
            pages = convert_from_path(pdf_file, dpi=200)
            
            # 取得PDF檔名
            pdf_name = pdf_file.stem
            
            # 儲存每一頁，直接輸出到OutputImages目錄
            for page_num, page in enumerate(pages, 1):
                output_filename = f"{pdf_name}_page_{page_num:03d}.png"
                output_path = output_folder / output_filename
                page.save(output_path, 'PNG')
            
            print(f"✅ 完成: {pdf_file.name} ({len(pages)} 頁)")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 失敗: {pdf_file.name} - {str(e)}")
    
    print(f"\n🎉 轉換完成! 成功: {success_count}/{len(pdf_files)}")
    print(f"📂 圖片已儲存到: {output_folder.absolute()}")

if __name__ == "__main__":
    print("=== 簡化版PDF轉圖片轉換器 ===")
    convert_pdfs_to_images() 