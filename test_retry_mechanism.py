#!/usr/bin/env python
# coding: utf-8

"""
測試API重試機制
"""

import requests
from unittest.mock import Mock, patch
from MeasurePointParser import setup_logging, upload_files_with_message

def test_retry_mechanism():
    """測試API重試機制"""
    print("測試API重試機制...")
    
    # 設定日誌
    logger = setup_logging()
    
    # 模擬檔案路徑
    test_files = ['test_file.pdf']
    test_message = "測試訊息"
    test_model = "gemini-2.5-flash"
    
    print("\n=== 測試1: 模擬API回應為null的情況 ===")
    
    # 模擬API回應為null
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {'response': None}
    mock_response.text = '{"response": null}'
    
    with patch('requests.post', return_value=mock_response):
        with patch('os.path.exists', return_value=True):
            with patch('MeasurePointParser.encode_file', return_value='fake_base64_content'):
                try:
                    response = upload_files_with_message(
                        test_files, 
                        test_message, 
                        model=test_model, 
                        max_retries=2, 
                        retry_delay=1,
                        logger=logger
                    )
                    print("意外成功 - 這不應該發生")
                except Exception as e:
                    print(f"預期的失敗: {str(e)}")
                    logger.info(f"測試1完成: {str(e)}")
    
    print("\n=== 測試2: 模擬HTTP 500錯誤 ===")
    
    # 模擬HTTP 500錯誤
    mock_response_500 = Mock()
    mock_response_500.status_code = 500
    mock_response_500.text = 'Internal Server Error'
    
    with patch('requests.post', return_value=mock_response_500):
        with patch('os.path.exists', return_value=True):
            with patch('MeasurePointParser.encode_file', return_value='fake_base64_content'):
                try:
                    response = upload_files_with_message(
                        test_files, 
                        test_message, 
                        model=test_model, 
                        max_retries=2, 
                        retry_delay=1,
                        logger=logger
                    )
                    print("意外成功 - 這不應該發生")
                except Exception as e:
                    print(f"預期的失敗: {str(e)}")
                    logger.info(f"測試2完成: {str(e)}")
    
    print("\n=== 測試3: 模擬第二次重試成功 ===")
    
    # 模擬第一次失敗，第二次成功
    responses = [
        Mock(status_code=500, text='Internal Server Error'),  # 第一次失敗
        Mock(status_code=200, json=lambda: {'response': 'success'}, text='{"response": "success"}')  # 第二次成功
    ]
    
    with patch('requests.post', side_effect=responses):
        with patch('os.path.exists', return_value=True):
            with patch('MeasurePointParser.encode_file', return_value='fake_base64_content'):
                try:
                    response = upload_files_with_message(
                        test_files, 
                        test_message, 
                        model=test_model, 
                        max_retries=3, 
                        retry_delay=1,
                        logger=logger
                    )
                    print("重試成功！")
                    logger.info("測試3完成: 重試機制成功")
                except Exception as e:
                    print(f"意外失敗: {str(e)}")
                    logger.error(f"測試3失敗: {str(e)}")

def test_retry_configuration():
    """測試重試配置參數"""
    print("\n=== 測試重試配置參數 ===")
    
    logger = setup_logging()
    
    # 測試不同的重試配置
    configs = [
        {'max_retries': 1, 'retry_delay': 1},
        {'max_retries': 3, 'retry_delay': 2},
        {'max_retries': 5, 'retry_delay': 1}
    ]
    
    for i, config in enumerate(configs, 1):
        logger.info(f"測試配置 {i}: max_retries={config['max_retries']}, retry_delay={config['retry_delay']}")
        print(f"配置 {i}: 最大重試次數={config['max_retries']}, 重試延遲={config['retry_delay']}秒")

if __name__ == "__main__":
    print("開始測試API重試機制")
    print("=" * 50)
    
    test_retry_mechanism()
    test_retry_configuration()
    
    print("\n" + "=" * 50)
    print("測試完成！請檢查日誌檔案中的重試記錄。")
    print("重試機制特點：")
    print("1. 預設最大重試3次")
    print("2. 每次重試間隔遞增（5秒、7秒、9秒）")
    print("3. 詳細的重試日誌記錄")
    print("4. 處理API回應為null的情況")
    print("5. 處理HTTP錯誤狀態碼")
