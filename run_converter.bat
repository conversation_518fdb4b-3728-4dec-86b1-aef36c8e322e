@echo off
chcp 65001 >nul
echo ===== PDF轉圖片轉換器 =====
echo.

REM 檢查Python是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤: 未找到Python，請先安裝Python
    pause
    exit /b 1
)

echo ✅ Python已安裝

REM 安裝必要套件
echo 📦 安裝必要套件...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ 套件安裝失敗
    pause
    exit /b 1
)

echo ✅ 套件安裝完成
echo.

REM 執行轉換程序
echo 🚀 開始執行PDF轉換...
python simple_pdf_converter.py

echo.
echo 按任意鍵結束...
pause >nul 