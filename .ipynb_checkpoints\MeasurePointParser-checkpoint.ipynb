{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ab6d291f-3ca2-40a1-9139-9e9248d5f87c", "metadata": {}, "outputs": [], "source": ["# client.py\n", "import requests\n", "import base64\n", "import json \n", "import os\n", "import glob\n", "import pandas as pd\n", "import io"]}, {"cell_type": "code", "execution_count": 2, "id": "f3322e46-7b65-45c2-9b48-41d9da86d68f", "metadata": {}, "outputs": [], "source": ["def encode_file(file_path):\n", "    \"\"\"將檔案轉換為 base64 編碼\"\"\"\n", "    with open(file_path, 'rb') as file:\n", "        return base64.b64encode(file.read()).decode('utf-8')\n", "\n", "def upload_files_with_message(file_paths, message=\"\", model = 'gemini-2.0-flash'):\n", "    \"\"\"上傳多個檔案和文字訊息到伺服器\"\"\"\n", "    url = 'http://172.16.2.25:5000/multimodalapi'\n", "    \n", "    # 準備檔案資料\n", "    files_data = []\n", "    for file_path in file_paths:\n", "        if os.path.exists(file_path):\n", "            filename = os.path.basename(file_path)\n", "            content = encode_file(file_path)\n", "            files_data.append({\n", "                'filename': filename,\n", "                'content': content\n", "            })\n", "    \n", "    # 準備要傳送的資料\n", "    payload = {\n", "        'files': files_data,\n", "        'message': message,\n", "        'model': model\n", "    }\n", "    \n", "    # 發送請求\n", "    try:\n", "        response = requests.post(\n", "            url,\n", "            json=payload,\n", "            headers={'Content-Type': 'application/json'}\n", "        )\n", "        \n", "        return response\n", "        # 輸出結果\n", "        print(f'Status Code: {response.status_code}')\n", "        # print('Response:', json.dumps(response.json(), indent=2))\n", "        print(response.text)\n", "        \n", "    except Exception as e:\n", "        print(f'Error: {str(e)}')\n", "        return f'Error: {str(e)}'\n", "    \n", "def parse_csv_response(csv_text):\n", "    \"\"\"解析API回傳的CSV格式資料\"\"\"\n", "    try:\n", "        # 將CSV文字轉換為DataFrame\n", "        csv_io = io.StringIO(csv_text)\n", "        df = pd.read_csv(csv_io)\n", "        \n", "        print(\"=== 解析結果 ===\")\n", "        print(f\"找到 {len(df)} 筆資料\")\n", "        print(f\"欄位: {list(df.columns)}\")\n", "        print(\"\\nDataFrame:\")\n", "        print(df)\n", "        \n", "        # 統計分析\n", "        if 'MARK' in df.columns:\n", "            print(f\"\\n=== MARK統計 ===\")\n", "            mark_counts = df['MARK'].value_counts()\n", "            print(mark_counts)\n", "        \n", "        if '層別' in df.columns:\n", "            print(f\"\\n=== 層別統計 ===\")\n", "            layer_counts = df['層別'].value_counts()\n", "            print(layer_counts)\n", "        \n", "        # 轉換為字典列表方便後續處理\n", "        records = df.to_dict('records')\n", "        print(f\"\\n=== 詳細資料 ===\")\n", "        for i, record in enumerate(records, 1):\n", "            print(f\"第{i}筆: {record}\")\n", "        \n", "        return df, records\n", "        \n", "    except Exception as e:\n", "        print(f\"CSV解析錯誤: {str(e)}\")\n", "        print(\"原始回傳內容:\")\n", "        print(csv_text)\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 4, "id": "155ec3cd-e40c-4c0c-91d3-efcaa7ce3722", "metadata": {"scrolled": true, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/plain": ["['PDFData/04C8U0020004T_S.pdf']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["files = glob.glob('PDFData/04C8U0020004T_S.pdf')\n", "print(len(files))\n", "files"]}, {"cell_type": "code", "execution_count": 5, "id": "3cb09f8b-21ba-4e3f-84d4-d392f8cdeafb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PDFData/04C8U0020004T_S.pdf\n", "None\n", "model : gemini-2.5-pro\n"]}], "source": ["i = 0\n", "print(files[i])\n", "message = ''\n", "if files[i].split('/')[-1][2:5].upper() == 'C8U':\n", "    model = 'gemini-2.5-pro'\n", "    message = \"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28\n", "光學點,B,寬度,<PERSON><PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期\n", "2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析\n", "3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析\n", "4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)\n", "5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同\n", "6.若目標欄解析沒有值(NA)的項目不用輸出呈現\n", "7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值\n", "8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊\n", "9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字\"\"\"\n", "else:\n", "    model = 'gemini-2.5-flash'\n", "    message = \"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28\n", "光學點,B,寬度,<PERSON><PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期\n", "2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析\n", "3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析\n", "4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)\n", "5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同\n", "6.若目標欄解析沒有值(NA)的項目不用輸出呈現\n", "7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值\n", "8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊\n", "9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字\"\"\"\n", "files_to_upload = [\n", "    files[i],\n", "]\n", "response = upload_files_with_message(files_to_upload, message, model)\n", "api_response = response.json()['response']\n", "print('model :',model)\n", "print(api_response)"]}, {"cell_type": "code", "execution_count": 9, "id": "f9b9fb58-ba99-4b0d-9f48-3ffc44d10ad0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>管制類別</th>\n", "      <th>符號</th>\n", "      <th>單位</th>\n", "      <th>原稿</th>\n", "      <th>工作片</th>\n", "      <th>目標值</th>\n", "      <th>客規下限</th>\n", "      <th>客規上限</th>\n", "      <th>客規規格(+)</th>\n", "      <th>客規規格(-)</th>\n", "      <th>規格類型(%or+-)</th>\n", "      <th>尺寸</th>\n", "      <th>量測位置</th>\n", "      <th>管制站別</th>\n", "      <th>管制目標值</th>\n", "      <th>管制下限</th>\n", "      <th>管制上限</th>\n", "      <th>製表者</th>\n", "      <th>日期</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>8.9</td>\n", "      <td>10.9</td>\n", "      <td>8.9</td>\n", "      <td>7.7</td>\n", "      <td>10.1</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>9.15</td>\n", "      <td>8.20</td>\n", "      <td>10.10</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>8.9</td>\n", "      <td>10.9</td>\n", "      <td>8.9</td>\n", "      <td>7.7</td>\n", "      <td>10.1</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>8.65</td>\n", "      <td>7.70</td>\n", "      <td>9.60</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>7.1</td>\n", "      <td>10.9</td>\n", "      <td>7.1</td>\n", "      <td>5.9</td>\n", "      <td>10.1</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>8.25</td>\n", "      <td>6.40</td>\n", "      <td>10.10</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>7.1</td>\n", "      <td>10.9</td>\n", "      <td>7.1</td>\n", "      <td>5.9</td>\n", "      <td>10.1</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>7.75</td>\n", "      <td>5.90</td>\n", "      <td>9.60</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>10.8</td>\n", "      <td>8.8</td>\n", "      <td>10.8</td>\n", "      <td>9.6</td>\n", "      <td>12.0</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>9.37</td>\n", "      <td>8.42</td>\n", "      <td>10.32</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>10.8</td>\n", "      <td>8.8</td>\n", "      <td>10.8</td>\n", "      <td>9.6</td>\n", "      <td>12.0</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>9.87</td>\n", "      <td>8.92</td>\n", "      <td>10.82</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>27.6</td>\n", "      <td>29.6</td>\n", "      <td>27.6</td>\n", "      <td>26.4</td>\n", "      <td>28.8</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>27.85</td>\n", "      <td>26.90</td>\n", "      <td>28.80</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>27.6</td>\n", "      <td>29.6</td>\n", "      <td>27.6</td>\n", "      <td>26.4</td>\n", "      <td>28.8</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>27.35</td>\n", "      <td>26.40</td>\n", "      <td>28.30</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>25.8</td>\n", "      <td>29.6</td>\n", "      <td>25.8</td>\n", "      <td>24.6</td>\n", "      <td>28.8</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>26.95</td>\n", "      <td>25.10</td>\n", "      <td>28.80</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>25.8</td>\n", "      <td>29.6</td>\n", "      <td>25.8</td>\n", "      <td>24.6</td>\n", "      <td>28.8</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>26.45</td>\n", "      <td>24.60</td>\n", "      <td>28.30</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>22.4</td>\n", "      <td>20.4</td>\n", "      <td>22.4</td>\n", "      <td>21.2</td>\n", "      <td>23.6</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>20.97</td>\n", "      <td>20.02</td>\n", "      <td>21.92</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>22.4</td>\n", "      <td>20.4</td>\n", "      <td>22.4</td>\n", "      <td>21.2</td>\n", "      <td>23.6</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>21.47</td>\n", "      <td>20.52</td>\n", "      <td>22.42</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>63.0</td>\n", "      <td>65.0</td>\n", "      <td>63.0</td>\n", "      <td>59.0</td>\n", "      <td>67.0</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>寬度</td>\n", "      <td>蝕銅</td>\n", "      <td>63.25</td>\n", "      <td>62.30</td>\n", "      <td>64.20</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>63.0</td>\n", "      <td>65.0</td>\n", "      <td>63.0</td>\n", "      <td>59.0</td>\n", "      <td>67.0</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>寬度</td>\n", "      <td>OQC</td>\n", "      <td>62.75</td>\n", "      <td>61.80</td>\n", "      <td>63.70</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>61.2</td>\n", "      <td>65.0</td>\n", "      <td>61.2</td>\n", "      <td>57.2</td>\n", "      <td>67.0</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>62.35</td>\n", "      <td>60.50</td>\n", "      <td>64.20</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>61.2</td>\n", "      <td>65.0</td>\n", "      <td>61.2</td>\n", "      <td>57.2</td>\n", "      <td>67.0</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>61.85</td>\n", "      <td>60.00</td>\n", "      <td>63.70</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>23.6</td>\n", "      <td>21.6</td>\n", "      <td>23.6</td>\n", "      <td>19.6</td>\n", "      <td>27.6</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>22.17</td>\n", "      <td>21.22</td>\n", "      <td>23.12</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>23.6</td>\n", "      <td>21.6</td>\n", "      <td>23.6</td>\n", "      <td>19.6</td>\n", "      <td>27.6</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>22.67</td>\n", "      <td>21.72</td>\n", "      <td>23.62</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>39.4</td>\n", "      <td>42.4</td>\n", "      <td>39.4</td>\n", "      <td>35.4</td>\n", "      <td>43.4</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>39.65</td>\n", "      <td>37.90</td>\n", "      <td>41.40</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>39.4</td>\n", "      <td>42.4</td>\n", "      <td>39.4</td>\n", "      <td>35.4</td>\n", "      <td>43.4</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>39.15</td>\n", "      <td>37.40</td>\n", "      <td>40.90</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>37.6</td>\n", "      <td>40.6</td>\n", "      <td>37.6</td>\n", "      <td>33.6</td>\n", "      <td>43.4</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>38.75</td>\n", "      <td>36.10</td>\n", "      <td>41.40</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>37.6</td>\n", "      <td>40.6</td>\n", "      <td>37.6</td>\n", "      <td>33.6</td>\n", "      <td>43.4</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>38.25</td>\n", "      <td>35.60</td>\n", "      <td>40.90</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      管制類別 符號   單位    原稿   工作片   目標值  客規下限  客規上限  客規規格(+)  客規規格(-)  \\\n", "0   SMD位置圖  A  MIL   8.9  10.9   8.9   7.7  10.1      1.2     -1.2   \n", "1   SMD位置圖  A  MIL   8.9  10.9   8.9   7.7  10.1      1.2     -1.2   \n", "2   SMD位置圖  A  MIL   7.1  10.9   7.1   5.9  10.1      3.0     -1.2   \n", "3   SMD位置圖  A  MIL   7.1  10.9   7.1   5.9  10.1      3.0     -1.2   \n", "4   SMD位置圖  A  MIL  10.8   8.8  10.8   9.6  12.0      1.2     -1.2   \n", "5   SMD位置圖  A  MIL  10.8   8.8  10.8   9.6  12.0      1.2     -1.2   \n", "6   SMD位置圖  B  MIL  27.6  29.6  27.6  26.4  28.8      1.2     -1.2   \n", "7   SMD位置圖  B  MIL  27.6  29.6  27.6  26.4  28.8      1.2     -1.2   \n", "8   SMD位置圖  B  MIL  25.8  29.6  25.8  24.6  28.8      3.0     -1.2   \n", "9   SMD位置圖  B  MIL  25.8  29.6  25.8  24.6  28.8      3.0     -1.2   \n", "10  SMD位置圖  B  MIL  22.4  20.4  22.4  21.2  23.6      1.2     -1.2   \n", "11  SMD位置圖  B  MIL  22.4  20.4  22.4  21.2  23.6      1.2     -1.2   \n", "12  SMD位置圖  C  MIL  63.0  65.0  63.0  59.0  67.0      4.0     -4.0   \n", "13  SMD位置圖  C  MIL  63.0  65.0  63.0  59.0  67.0      4.0     -4.0   \n", "14  SMD位置圖  C  MIL  61.2  65.0  61.2  57.2  67.0      5.8     -4.0   \n", "15  SMD位置圖  C  MIL  61.2  65.0  61.2  57.2  67.0      5.8     -4.0   \n", "16  SMD位置圖  C  MIL  23.6  21.6  23.6  19.6  27.6      4.0     -4.0   \n", "17  SMD位置圖  C  MIL  23.6  21.6  23.6  19.6  27.6      4.0     -4.0   \n", "18     光學點  D  MIL  39.4  42.4  39.4  35.4  43.4      4.0     -4.0   \n", "19     光學點  D  MIL  39.4  42.4  39.4  35.4  43.4      4.0     -4.0   \n", "20     光學點  D  MIL  37.6  40.6  37.6  33.6  43.4      5.8     -4.0   \n", "21     光學點  D  MIL  37.6  40.6  37.6  33.6  43.4      5.8     -4.0   \n", "\n", "   規格類型(%or+-)  尺寸 量測位置 管制站別  管制目標值   管制下限   管制上限  製表者        日期  \n", "0          +/-  下幅   下幅   蝕銅   9.15   8.20  10.10  汪素芬  2013/7/4  \n", "1          +/-  下幅   下幅  OQC   8.65   7.70   9.60  汪素芬  2013/7/4  \n", "2          +/-  上幅   上幅   蝕銅   8.25   6.40  10.10  汪素芬  2013/7/4  \n", "3          +/-  上幅   上幅  OQC   7.75   5.90   9.60  汪素芬  2013/7/4  \n", "4          +/-  間距   間距   蝕銅   9.37   8.42  10.32  汪素芬  2013/7/4  \n", "5          +/-  間距   間距  OQC   9.87   8.92  10.82  汪素芬  2013/7/4  \n", "6          +/-  下幅   下幅   蝕銅  27.85  26.90  28.80  汪素芬  2013/7/4  \n", "7          +/-  下幅   下幅  OQC  27.35  26.40  28.30  汪素芬  2013/7/4  \n", "8          +/-  上幅   上幅   蝕銅  26.95  25.10  28.80  汪素芬  2013/7/4  \n", "9          +/-  上幅   上幅  OQC  26.45  24.60  28.30  汪素芬  2013/7/4  \n", "10         +/-  間距   間距   蝕銅  20.97  20.02  21.92  汪素芬  2013/7/4  \n", "11         +/-  間距   間距  OQC  21.47  20.52  22.42  汪素芬  2013/7/4  \n", "12         +/-  寬度   寬度   蝕銅  63.25  62.30  64.20  汪素芬  2013/7/4  \n", "13         +/-  寬度   寬度  OQC  62.75  61.80  63.70  汪素芬  2013/7/4  \n", "14         +/-  上幅   上幅   蝕銅  62.35  60.50  64.20  汪素芬  2013/7/4  \n", "15         +/-  上幅   上幅  OQC  61.85  60.00  63.70  汪素芬  2013/7/4  \n", "16         +/-  間距   間距   蝕銅  22.17  21.22  23.12  汪素芬  2013/7/4  \n", "17         +/-  間距   間距  OQC  22.67  21.72  23.62  汪素芬  2013/7/4  \n", "18         +/-  下幅   下幅   蝕銅  39.65  37.90  41.40  汪素芬  2013/7/4  \n", "19         +/-  下幅   下幅  OQC  39.15  37.40  40.90  汪素芬  2013/7/4  \n", "20         +/-  上幅   上幅   蝕銅  38.75  36.10  41.40  汪素芬  2013/7/4  \n", "21         +/-  上幅   上幅  OQC  38.25  35.60  40.90  汪素芬  2013/7/4  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉\n", "lines = api_response.splitlines()\n", "if lines and (lines[0].strip() == '```csv' or lines[0].strip().lower() == 'csv'):\n", "    csv_content = '\\n'.join(lines[1:])\n", "else:\n", "    csv_content = api_response\n", "# 去掉最前後多餘的空白行，避免多讀到空列\n", "csv_content = csv_content.strip()\n", "# 如果字串末尾有單獨的 ```，去除它\n", "if csv_content.endswith('```'):\n", "    csv_content = csv_content[:-3].rstrip()\n", "# 使用 io.StringIO 將 CSV 內容轉為類似檔案的物件\n", "csv_buffer = io.StringIO(csv_content)\n", "# 讀取 CSV 內容到 DataFrame，並把 'NA' 當作 NaN 處理\n", "df = pd.read_csv(csv_buffer, keep_default_na=False)#, na_values=['NA'])\n", "# # 如果還有全空的列，可再刪除：\n", "# df = df.dropna(how='all')\n", "# 顯示 DataFrame 前幾列\n", "df"]}, {"cell_type": "code", "execution_count": 13, "id": "75fb27d5-7497-41f8-9bbf-ab119a179e77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```csv\n", "符號,層別\n", "A,C面&S面\n", "B,S面\n", "C,C面\n", "D,C面&S面\n", "```\n"]}], "source": ["message = f\"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "符號,層別\n", "A,C面&S面\n", "B,S面\n", "N/A,C面\n", "NA,C面\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.此份PDF工單點位資料有定義{len(list(set(df['符號'])))}種Mark點位：{'、'.join(list(set(df['符號'])))}，請幫我解析他們被定義在C面還是S面還是兩面都有。\n", "2.C、S面中的符號標示可能會轉90度\n", "3.如圖片中有太小或太過於糊的符號請先放大增加解析度再解析結果\n", "4.請直接輸出CSV格式表格，方便以python pandas讀取，不要有多餘的說明文字\"\"\"\n", "files_to_upload = [\n", "    files[i],\n", "]\n", "response = upload_files_with_message(files_to_upload, message, model = 'gemini-2.5-pro')\n", "api_response = response.json()['response']\n", "print(api_response)"]}, {"cell_type": "code", "execution_count": 14, "id": "51baa554-0c95-48ce-8204-0d4c08fc6aa7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>符號</th>\n", "      <th>層別</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>B</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  符號     層別\n", "0  A  C面&S面\n", "1  B     S面\n", "2  C     C面\n", "3  D  C面&S面"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉\n", "lines = api_response.splitlines()\n", "if lines and (lines[0].strip() == '```csv' or lines[0].strip().lower() == 'csv'):\n", "    csv_content = '\\n'.join(lines[1:])\n", "else:\n", "    csv_content = api_response\n", "# 去掉最前後多餘的空白行，避免多讀到空列\n", "csv_content = csv_content.strip()\n", "# 如果字串末尾有單獨的 ```，去除它\n", "if csv_content.endswith('```'):\n", "    csv_content = csv_content[:-3].rstrip()\n", "# 使用 io.StringIO 將 CSV 內容轉為類似檔案的物件\n", "csv_buffer = io.StringIO(csv_content)\n", "# 讀取 CSV 內容到 DataFrame，並把 'NA' 當作 NaN 處理\n", "df_mark = pd.read_csv(csv_buffer, keep_default_na=False)#, na_values=['NA'])\n", "# 如果還有全空的列，可再刪除：\n", "df_mark = df_mark.dropna(how='all')\n", "# 顯示 DataFrame 前幾列\n", "df_mark"]}, {"cell_type": "code", "execution_count": 15, "id": "6f931bf3-b4be-44ac-9ae0-303cd5b55c2b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>管制類別</th>\n", "      <th>符號</th>\n", "      <th>單位</th>\n", "      <th>原稿</th>\n", "      <th>工作片</th>\n", "      <th>目標值</th>\n", "      <th>客規下限</th>\n", "      <th>客規上限</th>\n", "      <th>客規規格(+)</th>\n", "      <th>客規規格(-)</th>\n", "      <th>規格類型(%or+-)</th>\n", "      <th>尺寸</th>\n", "      <th>量測位置</th>\n", "      <th>管制站別</th>\n", "      <th>管制目標值</th>\n", "      <th>管制下限</th>\n", "      <th>管制上限</th>\n", "      <th>製表者</th>\n", "      <th>日期</th>\n", "      <th>層別</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>8.9</td>\n", "      <td>10.9</td>\n", "      <td>8.9</td>\n", "      <td>7.7</td>\n", "      <td>10.1</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>9.15</td>\n", "      <td>8.20</td>\n", "      <td>10.10</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>8.9</td>\n", "      <td>10.9</td>\n", "      <td>8.9</td>\n", "      <td>7.7</td>\n", "      <td>10.1</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>8.65</td>\n", "      <td>7.70</td>\n", "      <td>9.60</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>7.1</td>\n", "      <td>10.9</td>\n", "      <td>7.1</td>\n", "      <td>5.9</td>\n", "      <td>10.1</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>8.25</td>\n", "      <td>6.40</td>\n", "      <td>10.10</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>7.1</td>\n", "      <td>10.9</td>\n", "      <td>7.1</td>\n", "      <td>5.9</td>\n", "      <td>10.1</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>7.75</td>\n", "      <td>5.90</td>\n", "      <td>9.60</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>10.8</td>\n", "      <td>8.8</td>\n", "      <td>10.8</td>\n", "      <td>9.6</td>\n", "      <td>12.0</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>9.37</td>\n", "      <td>8.42</td>\n", "      <td>10.32</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SMD位置圖</td>\n", "      <td>A</td>\n", "      <td>MIL</td>\n", "      <td>10.8</td>\n", "      <td>8.8</td>\n", "      <td>10.8</td>\n", "      <td>9.6</td>\n", "      <td>12.0</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>9.87</td>\n", "      <td>8.92</td>\n", "      <td>10.82</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>27.6</td>\n", "      <td>29.6</td>\n", "      <td>27.6</td>\n", "      <td>26.4</td>\n", "      <td>28.8</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>27.85</td>\n", "      <td>26.90</td>\n", "      <td>28.80</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>27.6</td>\n", "      <td>29.6</td>\n", "      <td>27.6</td>\n", "      <td>26.4</td>\n", "      <td>28.8</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>27.35</td>\n", "      <td>26.40</td>\n", "      <td>28.30</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>25.8</td>\n", "      <td>29.6</td>\n", "      <td>25.8</td>\n", "      <td>24.6</td>\n", "      <td>28.8</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>26.95</td>\n", "      <td>25.10</td>\n", "      <td>28.80</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>25.8</td>\n", "      <td>29.6</td>\n", "      <td>25.8</td>\n", "      <td>24.6</td>\n", "      <td>28.8</td>\n", "      <td>3.0</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>26.45</td>\n", "      <td>24.60</td>\n", "      <td>28.30</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>22.4</td>\n", "      <td>20.4</td>\n", "      <td>22.4</td>\n", "      <td>21.2</td>\n", "      <td>23.6</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>20.97</td>\n", "      <td>20.02</td>\n", "      <td>21.92</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>SMD位置圖</td>\n", "      <td>B</td>\n", "      <td>MIL</td>\n", "      <td>22.4</td>\n", "      <td>20.4</td>\n", "      <td>22.4</td>\n", "      <td>21.2</td>\n", "      <td>23.6</td>\n", "      <td>1.2</td>\n", "      <td>-1.2</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>21.47</td>\n", "      <td>20.52</td>\n", "      <td>22.42</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>63.0</td>\n", "      <td>65.0</td>\n", "      <td>63.0</td>\n", "      <td>59.0</td>\n", "      <td>67.0</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>寬度</td>\n", "      <td>蝕銅</td>\n", "      <td>63.25</td>\n", "      <td>62.30</td>\n", "      <td>64.20</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>63.0</td>\n", "      <td>65.0</td>\n", "      <td>63.0</td>\n", "      <td>59.0</td>\n", "      <td>67.0</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>寬度</td>\n", "      <td>OQC</td>\n", "      <td>62.75</td>\n", "      <td>61.80</td>\n", "      <td>63.70</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>61.2</td>\n", "      <td>65.0</td>\n", "      <td>61.2</td>\n", "      <td>57.2</td>\n", "      <td>67.0</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>62.35</td>\n", "      <td>60.50</td>\n", "      <td>64.20</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>61.2</td>\n", "      <td>65.0</td>\n", "      <td>61.2</td>\n", "      <td>57.2</td>\n", "      <td>67.0</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>61.85</td>\n", "      <td>60.00</td>\n", "      <td>63.70</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>23.6</td>\n", "      <td>21.6</td>\n", "      <td>23.6</td>\n", "      <td>19.6</td>\n", "      <td>27.6</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>蝕銅</td>\n", "      <td>22.17</td>\n", "      <td>21.22</td>\n", "      <td>23.12</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>SMD位置圖</td>\n", "      <td>C</td>\n", "      <td>MIL</td>\n", "      <td>23.6</td>\n", "      <td>21.6</td>\n", "      <td>23.6</td>\n", "      <td>19.6</td>\n", "      <td>27.6</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>間距</td>\n", "      <td>OQC</td>\n", "      <td>22.67</td>\n", "      <td>21.72</td>\n", "      <td>23.62</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>39.4</td>\n", "      <td>42.4</td>\n", "      <td>39.4</td>\n", "      <td>35.4</td>\n", "      <td>43.4</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>蝕銅</td>\n", "      <td>39.65</td>\n", "      <td>37.90</td>\n", "      <td>41.40</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>39.4</td>\n", "      <td>42.4</td>\n", "      <td>39.4</td>\n", "      <td>35.4</td>\n", "      <td>43.4</td>\n", "      <td>4.0</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>下幅</td>\n", "      <td>下幅</td>\n", "      <td>OQC</td>\n", "      <td>39.15</td>\n", "      <td>37.40</td>\n", "      <td>40.90</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>37.6</td>\n", "      <td>40.6</td>\n", "      <td>37.6</td>\n", "      <td>33.6</td>\n", "      <td>43.4</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>38.75</td>\n", "      <td>36.10</td>\n", "      <td>41.40</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>光學點</td>\n", "      <td>D</td>\n", "      <td>MIL</td>\n", "      <td>37.6</td>\n", "      <td>40.6</td>\n", "      <td>37.6</td>\n", "      <td>33.6</td>\n", "      <td>43.4</td>\n", "      <td>5.8</td>\n", "      <td>-4.0</td>\n", "      <td>+/-</td>\n", "      <td>上幅</td>\n", "      <td>上幅</td>\n", "      <td>OQC</td>\n", "      <td>38.25</td>\n", "      <td>35.60</td>\n", "      <td>40.90</td>\n", "      <td>汪素芬</td>\n", "      <td>2013/7/4</td>\n", "      <td>C面&amp;S面</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      管制類別 符號   單位    原稿   工作片   目標值  客規下限  客規上限  客規規格(+)  客規規格(-)  \\\n", "0   SMD位置圖  A  MIL   8.9  10.9   8.9   7.7  10.1      1.2     -1.2   \n", "1   SMD位置圖  A  MIL   8.9  10.9   8.9   7.7  10.1      1.2     -1.2   \n", "2   SMD位置圖  A  MIL   7.1  10.9   7.1   5.9  10.1      3.0     -1.2   \n", "3   SMD位置圖  A  MIL   7.1  10.9   7.1   5.9  10.1      3.0     -1.2   \n", "4   SMD位置圖  A  MIL  10.8   8.8  10.8   9.6  12.0      1.2     -1.2   \n", "5   SMD位置圖  A  MIL  10.8   8.8  10.8   9.6  12.0      1.2     -1.2   \n", "6   SMD位置圖  B  MIL  27.6  29.6  27.6  26.4  28.8      1.2     -1.2   \n", "7   SMD位置圖  B  MIL  27.6  29.6  27.6  26.4  28.8      1.2     -1.2   \n", "8   SMD位置圖  B  MIL  25.8  29.6  25.8  24.6  28.8      3.0     -1.2   \n", "9   SMD位置圖  B  MIL  25.8  29.6  25.8  24.6  28.8      3.0     -1.2   \n", "10  SMD位置圖  B  MIL  22.4  20.4  22.4  21.2  23.6      1.2     -1.2   \n", "11  SMD位置圖  B  MIL  22.4  20.4  22.4  21.2  23.6      1.2     -1.2   \n", "12  SMD位置圖  C  MIL  63.0  65.0  63.0  59.0  67.0      4.0     -4.0   \n", "13  SMD位置圖  C  MIL  63.0  65.0  63.0  59.0  67.0      4.0     -4.0   \n", "14  SMD位置圖  C  MIL  61.2  65.0  61.2  57.2  67.0      5.8     -4.0   \n", "15  SMD位置圖  C  MIL  61.2  65.0  61.2  57.2  67.0      5.8     -4.0   \n", "16  SMD位置圖  C  MIL  23.6  21.6  23.6  19.6  27.6      4.0     -4.0   \n", "17  SMD位置圖  C  MIL  23.6  21.6  23.6  19.6  27.6      4.0     -4.0   \n", "18     光學點  D  MIL  39.4  42.4  39.4  35.4  43.4      4.0     -4.0   \n", "19     光學點  D  MIL  39.4  42.4  39.4  35.4  43.4      4.0     -4.0   \n", "20     光學點  D  MIL  37.6  40.6  37.6  33.6  43.4      5.8     -4.0   \n", "21     光學點  D  MIL  37.6  40.6  37.6  33.6  43.4      5.8     -4.0   \n", "\n", "   規格類型(%or+-)  尺寸 量測位置 管制站別  管制目標值   管制下限   管制上限  製表者        日期     層別  \n", "0          +/-  下幅   下幅   蝕銅   9.15   8.20  10.10  汪素芬  2013/7/4  C面&S面  \n", "1          +/-  下幅   下幅  OQC   8.65   7.70   9.60  汪素芬  2013/7/4  C面&S面  \n", "2          +/-  上幅   上幅   蝕銅   8.25   6.40  10.10  汪素芬  2013/7/4  C面&S面  \n", "3          +/-  上幅   上幅  OQC   7.75   5.90   9.60  汪素芬  2013/7/4  C面&S面  \n", "4          +/-  間距   間距   蝕銅   9.37   8.42  10.32  汪素芬  2013/7/4  C面&S面  \n", "5          +/-  間距   間距  OQC   9.87   8.92  10.82  汪素芬  2013/7/4  C面&S面  \n", "6          +/-  下幅   下幅   蝕銅  27.85  26.90  28.80  汪素芬  2013/7/4     S面  \n", "7          +/-  下幅   下幅  OQC  27.35  26.40  28.30  汪素芬  2013/7/4     S面  \n", "8          +/-  上幅   上幅   蝕銅  26.95  25.10  28.80  汪素芬  2013/7/4     S面  \n", "9          +/-  上幅   上幅  OQC  26.45  24.60  28.30  汪素芬  2013/7/4     S面  \n", "10         +/-  間距   間距   蝕銅  20.97  20.02  21.92  汪素芬  2013/7/4     S面  \n", "11         +/-  間距   間距  OQC  21.47  20.52  22.42  汪素芬  2013/7/4     S面  \n", "12         +/-  寬度   寬度   蝕銅  63.25  62.30  64.20  汪素芬  2013/7/4     C面  \n", "13         +/-  寬度   寬度  OQC  62.75  61.80  63.70  汪素芬  2013/7/4     C面  \n", "14         +/-  上幅   上幅   蝕銅  62.35  60.50  64.20  汪素芬  2013/7/4     C面  \n", "15         +/-  上幅   上幅  OQC  61.85  60.00  63.70  汪素芬  2013/7/4     C面  \n", "16         +/-  間距   間距   蝕銅  22.17  21.22  23.12  汪素芬  2013/7/4     C面  \n", "17         +/-  間距   間距  OQC  22.67  21.72  23.62  汪素芬  2013/7/4     C面  \n", "18         +/-  下幅   下幅   蝕銅  39.65  37.90  41.40  汪素芬  2013/7/4  C面&S面  \n", "19         +/-  下幅   下幅  OQC  39.15  37.40  40.90  汪素芬  2013/7/4  C面&S面  \n", "20         +/-  上幅   上幅   蝕銅  38.75  36.10  41.40  汪素芬  2013/7/4  C面&S面  \n", "21         +/-  上幅   上幅  OQC  38.25  35.60  40.90  汪素芬  2013/7/4  C面&S面  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 用 merge 進行左連結（以 df 為主，df_mark_sub 補上對應的「層別」）\n", "df_merged = pd.merge(\n", "    df,                # 左表\n", "    df_mark,       # 右表\n", "    on='符號',         # 以哪個欄位當 key 去比對\n", "    how='inner'         # how 可以改成 'inner'、'right'、'outer' 看你要哪種連結\n", ")\n", "\n", "# 檢查合併後的結果\n", "df_merged"]}, {"cell_type": "code", "execution_count": 9, "id": "c9b4fc72-c606-45d5-997c-de3b32583fa7", "metadata": {}, "outputs": [], "source": ["df_merged.to_csv(files[i].replace('.pdf','.csv'), index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": null, "id": "b7e0a9a1-fa45-45c0-9045-37dd9f93dba9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}