{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ab6d291f-3ca2-40a1-9139-9e9248d5f87c", "metadata": {}, "outputs": [], "source": ["# client.py\n", "import requests\n", "import base64\n", "import json \n", "import os\n", "import glob\n", "import pandas as pd\n", "import io"]}, {"cell_type": "code", "execution_count": 2, "id": "f3322e46-7b65-45c2-9b48-41d9da86d68f", "metadata": {}, "outputs": [], "source": ["def encode_file(file_path):\n", "    \"\"\"將檔案轉換為 base64 編碼\"\"\"\n", "    with open(file_path, 'rb') as file:\n", "        return base64.b64encode(file.read()).decode('utf-8')\n", "\n", "def upload_files_with_message(file_paths, message=\"\", model = 'gemini-2.0-flash'):\n", "    \"\"\"上傳多個檔案和文字訊息到伺服器\"\"\"\n", "    url = 'http://172.16.2.25:5000/multimodalapi'\n", "    \n", "    # 準備檔案資料\n", "    files_data = []\n", "    for file_path in file_paths:\n", "        if os.path.exists(file_path):\n", "            filename = os.path.basename(file_path)\n", "            content = encode_file(file_path)\n", "            files_data.append({\n", "                'filename': filename,\n", "                'content': content\n", "            })\n", "    \n", "    # 準備要傳送的資料\n", "    payload = {\n", "        'files': files_data,\n", "        'message': message,\n", "        'model': model\n", "    }\n", "    \n", "    # 發送請求\n", "    try:\n", "        response = requests.post(\n", "            url,\n", "            json=payload,\n", "            headers={'Content-Type': 'application/json'}\n", "        )\n", "        \n", "        return response\n", "        # 輸出結果\n", "        print(f'Status Code: {response.status_code}')\n", "        # print('Response:', json.dumps(response.json(), indent=2))\n", "        print(response.text)\n", "        \n", "    except Exception as e:\n", "        print(f'Error: {str(e)}')\n", "        return f'Error: {str(e)}'\n", "    \n", "def parse_csv_response(csv_text):\n", "    \"\"\"解析API回傳的CSV格式資料\"\"\"\n", "    try:\n", "        # 將CSV文字轉換為DataFrame\n", "        csv_io = io.StringIO(csv_text)\n", "        df = pd.read_csv(csv_io)\n", "        \n", "        print(\"=== 解析結果 ===\")\n", "        print(f\"找到 {len(df)} 筆資料\")\n", "        print(f\"欄位: {list(df.columns)}\")\n", "        print(\"\\nDataFrame:\")\n", "        print(df)\n", "        \n", "        # 統計分析\n", "        if 'MARK' in df.columns:\n", "            print(f\"\\n=== MARK統計 ===\")\n", "            mark_counts = df['MARK'].value_counts()\n", "            print(mark_counts)\n", "        \n", "        if '層別' in df.columns:\n", "            print(f\"\\n=== 層別統計 ===\")\n", "            layer_counts = df['層別'].value_counts()\n", "            print(layer_counts)\n", "        \n", "        # 轉換為字典列表方便後續處理\n", "        records = df.to_dict('records')\n", "        print(f\"\\n=== 詳細資料 ===\")\n", "        for i, record in enumerate(records, 1):\n", "            print(f\"第{i}筆: {record}\")\n", "        \n", "        return df, records\n", "        \n", "    except Exception as e:\n", "        print(f\"CSV解析錯誤: {str(e)}\")\n", "        print(\"原始回傳內容:\")\n", "        print(csv_text)\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 3, "id": "155ec3cd-e40c-4c0c-91d3-efcaa7ce3722", "metadata": {"scrolled": true, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}, {"data": {"text/plain": ["['PDFData/04C8U0020004T_S.PDF']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["files = glob.glob('PDFData/04C8U0020004T_S.PDF')\n", "print(len(files))\n", "files"]}, {"cell_type": "code", "execution_count": null, "id": "3cb09f8b-21ba-4e3f-84d4-d392f8cdeafb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PDFData/04C8U0020004T_S.PDF\n"]}], "source": ["i = 0\n", "print(files[i])\n", "message = ''\n", "if files[i].split('/')[-1][2:5].upper() == 'C8U':\n", "    model = 'gemini-2.5-pro'\n", "    message = \"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28\n", "光學點,B,寬度,<PERSON><PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期\n", "2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析\n", "3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析\n", "4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)\n", "5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同\n", "6.若目標欄解析沒有值(NA)的項目不用輸出呈現\n", "7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值\n", "8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊\n", "9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字\"\"\"\n", "else:\n", "    model = 'gemini-2.5-flash'\n", "    message = \"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "管制類別,符號,尺寸,單位,原稿,工作片,目標值,客規下限,客規上限,客戶規格(+),客戶規格(-),規格類型(%or+-),量測位置,管制站別,管制目標值,管制下限,管制上限,製表者,日期\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,蝕銅,12.35,11.5,13.2,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,粗化,12.25,11.4,13.1,陳逸婷,2017/4/28\n", "SMD,A,寬度,MIL,12.6,13.6,12,11,13,0.4,-1.6,+/-,量下幅,OQC,12,11,13,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,蝕銅,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,粗化,NA,NA,NA,陳逸婷,2017/4/28\n", "SMD,A,間距,MIL,NA,NA,NA,NA,NA,0.4,-1.6,+/-,無規定,OQC,NA,NA,NA,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,蝕銅,23.35,22.5,24.2,陳逸婷,2017/4/28\n", "光學點,B,寬度,MI<PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,粗化,23.25,22.4,24.1,陳逸婷,2017/4/28\n", "光學點,B,寬度,<PERSON><PERSON>,23.6,25,23,22,24,20,-20,%,量下幅,OQC,23,22,24,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,蝕銅,102.15,101.3,103,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,粗化,102.05,101.2,102,陳逸婷,2017/4/28\n", "光學點,<PERSON>,長度,MIL,102.4,103.8,101.8,100.8,102.8,-3.1,3.1,+/-,量下幅,OQC,101.8,100.8,102.8,陳逸婷,2017/4/28\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.輸出表格欄位順序為(1)管制類別、(2)符號、(3)單位、(4)原稿、(5)工作片、(6)目標值、(7)客規下限、(8)客規上限、(9)客規規格(+)、(10)客規規格(-)、(11)規格類型(%or+-)、(12)尺寸、(13)量測位置、(14)管制站別、(15)管制目標值、(16)管制下限、(17)管制上限、(18)製表者、(19)日期\n", "2.管制站別可能有層別、蝕銅、粗化、OQC...請注意中文字的解析\n", "3.符號可能有英文大寫A、B、C......，也可能是N/A、NA、空值，請確實解析\n", "4.(9)客規規格(+)、(10)客規規格(-)請解析客戶規格欄位右側的2格資料，注意參考正與負數值跟百分比型的差填(11)規格類型(%or+-)\n", "5.群組1[無光學點：尺寸為寬度的量測位置可能有量上幅、量下幅、無規定... ，尺寸為間距的量測位置可能有無規定.....]、群組2[有光學點：尺寸可能有上幅、下幅、間距.....，量測位置與尺寸產生值相同]，需依照原參考資料呈現，只能擇一解析請注意中文解析且同一份資料內容皆需相同\n", "6.若目標欄解析沒有值(NA)的項目不用輸出呈現\n", "7.輸出CSV表格數值需依PDF解析資料為主，排除點位模版的參考值\n", "8.每列有19個欄位，最終排版請確認欄位名稱與解析值的y意義，須作對齊\n", "9.請單純輸出轉換後的CSV表格，方便Python pandas讀取，不要有多其餘的文字\"\"\"\n", "files_to_upload = [\n", "    files[i],\n", "]\n", "response = upload_files_with_message(files_to_upload, message, model)\n", "api_response = response.json()['response']\n", "print(api_response)\n", "print('model :',model)"]}, {"cell_type": "code", "execution_count": 5, "id": "f9b9fb58-ba99-4b0d-9f48-3ffc44d10ad0", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'splitlines'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m lines = \u001b[43mapi_response\u001b[49m\u001b[43m.\u001b[49m\u001b[43msplitlines\u001b[49m()\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m lines \u001b[38;5;129;01mand\u001b[39;00m (lines[\u001b[32m0\u001b[39m].strip() == \u001b[33m'\u001b[39m\u001b[33m```csv\u001b[39m\u001b[33m'\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m lines[\u001b[32m0\u001b[39m].strip().lower() == \u001b[33m'\u001b[39m\u001b[33mcsv\u001b[39m\u001b[33m'\u001b[39m):\n\u001b[32m      4\u001b[39m     csv_content = \u001b[33m'\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m'\u001b[39m.join(lines[\u001b[32m1\u001b[39m:])\n", "\u001b[31mAttributeError\u001b[39m: 'NoneType' object has no attribute 'splitlines'"]}], "source": ["# 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉\n", "lines = api_response.splitlines()\n", "if lines and (lines[0].strip() == '```csv' or lines[0].strip().lower() == 'csv'):\n", "    csv_content = '\\n'.join(lines[1:])\n", "else:\n", "    csv_content = api_response\n", "# 去掉最前後多餘的空白行，避免多讀到空列\n", "csv_content = csv_content.strip()\n", "# 如果字串末尾有單獨的 ```，去除它\n", "if csv_content.endswith('```'):\n", "    csv_content = csv_content[:-3].rstrip()\n", "# 使用 io.StringIO 將 CSV 內容轉為類似檔案的物件\n", "csv_buffer = io.StringIO(csv_content)\n", "# 讀取 CSV 內容到 DataFrame，並把 'NA' 當作 NaN 處理\n", "df = pd.read_csv(csv_buffer, keep_default_na=False)#, na_values=['NA'])\n", "# # 如果還有全空的列，可再刪除：\n", "# df = df.dropna(how='all')\n", "# 顯示 DataFrame 前幾列\n", "df"]}, {"cell_type": "code", "execution_count": 92, "id": "75fb27d5-7497-41f8-9bbf-ab119a179e77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```csv\n", "符號,層別\n", "NA,C面\n", "```\n"]}], "source": ["message = f\"\"\"您是一位PCB量測點位解析專家，須要由點位工單資料中解析出如下CSV點位模版資料：\n", "\n", "符號,層別\n", "A,C面&S面\n", "B,S面\n", "N/A,C面\n", "NA,C面\n", "\n", "請幫我參考CSV點位模版，轉換PDF中的工單點位資料，並輸出轉換出的CSV表格，\n", "轉換時請注意以下幾點：\n", "1.此份PDF工單點位資料有定義{len(list(set(df['符號'])))}種Mark點位：{'、'.join(list(set(df['符號'])))}，請幫我解析他們被定義在C面還是S面還是兩面都有。\n", "2.C、S面中的符號標示可能會轉90度\n", "3.如圖片中有太小或太過於糊的符號請先放大增加解析度再解析結果\n", "4.請直接輸出CSV格式表格，方便以python pandas讀取，不要有多餘的說明文字\"\"\"\n", "files_to_upload = [\n", "    files[i],\n", "]\n", "# response = upload_files_with_message(files_to_upload, message, model = 'gemini-2.5-pro')\n", "response = upload_files_with_message(files_to_upload, message, model = 'gemini-2.5-flash')\n", "api_response = response.json()['response']\n", "print(api_response)"]}, {"cell_type": "code", "execution_count": 93, "id": "51baa554-0c95-48ce-8204-0d4c08fc6aa7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>符號</th>\n", "      <th>層別</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NA</td>\n", "      <td>C面</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   符號  層別\n", "0  NA  C面"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["# 若字串中包含前導的 ```csv 或 csv 標記，我們可以先將它去掉\n", "lines = api_response.splitlines()\n", "if lines and (lines[0].strip() == '```csv' or lines[0].strip().lower() == 'csv'):\n", "    csv_content = '\\n'.join(lines[1:])\n", "else:\n", "    csv_content = api_response\n", "# 去掉最前後多餘的空白行，避免多讀到空列\n", "csv_content = csv_content.strip()\n", "# 如果字串末尾有單獨的 ```，去除它\n", "if csv_content.endswith('```'):\n", "    csv_content = csv_content[:-3].rstrip()\n", "# 使用 io.StringIO 將 CSV 內容轉為類似檔案的物件\n", "csv_buffer = io.StringIO(csv_content)\n", "# 讀取 CSV 內容到 DataFrame，並把 'NA' 當作 NaN 處理\n", "df_mark = pd.read_csv(csv_buffer, keep_default_na=False)#, na_values=['NA'])\n", "# 如果還有全空的列，可再刪除：\n", "df_mark = df_mark.dropna(how='all')\n", "# 顯示 DataFrame 前幾列\n", "df_mark"]}, {"cell_type": "code", "execution_count": 94, "id": "6f931bf3-b4be-44ac-9ae0-303cd5b55c2b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>管制類別</th>\n", "      <th>符號</th>\n", "      <th>單位</th>\n", "      <th>原稿</th>\n", "      <th>工作片</th>\n", "      <th>目標值</th>\n", "      <th>客規下限</th>\n", "      <th>客規上限</th>\n", "      <th>客規規格(+)</th>\n", "      <th>客規規格(-)</th>\n", "      <th>規格類型(%or+-)</th>\n", "      <th>尺寸</th>\n", "      <th>量測位置</th>\n", "      <th>管制站別</th>\n", "      <th>管制目標值</th>\n", "      <th>管制下限</th>\n", "      <th>管制上限</th>\n", "      <th>製表者</th>\n", "      <th>日期</th>\n", "      <th>層別</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>15.00</td>\n", "      <td>17.10</td>\n", "      <td>15.00</td>\n", "      <td>14.00</td>\n", "      <td>16.00</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>量上幅</td>\n", "      <td>蝕銅</td>\n", "      <td>15.40</td>\n", "      <td>14.50</td>\n", "      <td>16.30</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>15.00</td>\n", "      <td>17.10</td>\n", "      <td>15.00</td>\n", "      <td>14.00</td>\n", "      <td>16.00</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>量上幅</td>\n", "      <td>粗化</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>15.00</td>\n", "      <td>17.10</td>\n", "      <td>15.00</td>\n", "      <td>14.00</td>\n", "      <td>16.00</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>寬度</td>\n", "      <td>量上幅</td>\n", "      <td>OQC</td>\n", "      <td>15.00</td>\n", "      <td>14.00</td>\n", "      <td>16.00</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>無規定</td>\n", "      <td>蝕銅</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>無規定</td>\n", "      <td>粗化</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>SMD</td>\n", "      <td>NA</td>\n", "      <td>MIL</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>+/-</td>\n", "      <td>間距</td>\n", "      <td>無規定</td>\n", "      <td>OQC</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>NA</td>\n", "      <td>林玥君</td>\n", "      <td>2025/5/20</td>\n", "      <td>C面</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  管制類別  符號   單位     原稿    工作片    目標值   客規下限   客規上限  客規規格(+)  客規規格(-)  \\\n", "0  SMD  NA  MIL  15.00  17.10  15.00  14.00  16.00        1       -1   \n", "1  SMD  NA  MIL  15.00  17.10  15.00  14.00  16.00        1       -1   \n", "2  SMD  NA  MIL  15.00  17.10  15.00  14.00  16.00        1       -1   \n", "3  SMD  NA  MIL     NA     NA     NA     NA     NA        1       -1   \n", "4  SMD  NA  MIL     NA     NA     NA     NA     NA        1       -1   \n", "5  SMD  NA  MIL     NA     NA     NA     NA     NA        1       -1   \n", "\n", "  規格類型(%or+-)  尺寸 量測位置 管制站別  管制目標值   管制下限   管制上限  製表者         日期  層別  \n", "0         +/-  寬度  量上幅   蝕銅  15.40  14.50  16.30  林玥君  2025/5/20  C面  \n", "1         +/-  寬度  量上幅   粗化     NA     NA     NA  林玥君  2025/5/20  C面  \n", "2         +/-  寬度  量上幅  OQC  15.00  14.00  16.00  林玥君  2025/5/20  C面  \n", "3         +/-  間距  無規定   蝕銅     NA     NA     NA  林玥君  2025/5/20  C面  \n", "4         +/-  間距  無規定   粗化     NA     NA     NA  林玥君  2025/5/20  C面  \n", "5         +/-  間距  無規定  OQC     NA     NA     NA  林玥君  2025/5/20  C面  "]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["# 用 merge 進行左連結（以 df 為主，df_mark_sub 補上對應的「層別」）\n", "df_merged = pd.merge(\n", "    df,                # 左表\n", "    df_mark,       # 右表\n", "    on='符號',         # 以哪個欄位當 key 去比對\n", "    how='inner'         # how 可以改成 'inner'、'right'、'outer' 看你要哪種連結\n", ")\n", "\n", "# 檢查合併後的結果\n", "df_merged"]}, {"cell_type": "code", "execution_count": 9, "id": "c9b4fc72-c606-45d5-997c-de3b32583fa7", "metadata": {}, "outputs": [], "source": ["df_merged.to_csv(files[i].replace('.pdf','.csv'), index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": null, "id": "b7e0a9a1-fa45-45c0-9045-37dd9f93dba9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}