#!/usr/bin/env python
# coding: utf-8

"""
將空的CSV文件名稱轉換為對應的PDF文件名稱
"""

def convert_csv_to_pdf_names(csv_names_string):
    """
    將逗號分隔的CSV文件名稱轉換為PDF文件名稱
    
    參數:
        csv_names_string: 逗號分隔的CSV文件名稱字串
    
    回傳:
        逗號分隔的PDF文件名稱字串
    """
    # 分割CSV文件名稱
    csv_files = [name.strip() for name in csv_names_string.split(',')]
    
    # 轉換為PDF文件名稱
    pdf_files = []
    for csv_file in csv_files:
        if csv_file.endswith('_S.csv'):
            # 移除 '_S.csv' 後綴，添加 '.pdf'
            pdf_name = csv_file.replace('_S.csv', '.pdf')
            pdf_files.append(pdf_name)
        elif csv_file.endswith('.csv'):
            # 如果只是 '.csv' 結尾，直接替換
            pdf_name = csv_file.replace('.csv', '.pdf')
            pdf_files.append(pdf_name)
        else:
            # 如果沒有副檔名，直接添加 '.pdf'
            pdf_files.append(csv_file + '.pdf')
    
    return ','.join(pdf_files)

def main():
    """主程式"""
    print("CSV文件名稱轉PDF文件名稱工具")
    print("=" * 50)
    
    # 您提供的空CSV文件清單
    empty_csv_files = "03C112570002TM_S.csv,03C112670001TR_S.csv,03C112670001T_S.csv,03C8U0110001TRB_S.csv,03CSN4650003R_S.csv,03CSN6250004T_S.csv,04CSN2150004T_S.csv,04CSN2780001_S.csv,04CSN2790001_S.csv,06B5P0190001V_S.csv"
    
    print("原始空CSV文件清單:")
    print(empty_csv_files)
    print()
    
    # 轉換為PDF文件名稱
    pdf_files = convert_csv_to_pdf_names(empty_csv_files)
    
    print("轉換後的PDF文件清單:")
    print(pdf_files)
    print()
    
    # 分別顯示每個文件
    pdf_list = pdf_files.split(',')
    print("詳細PDF文件清單:")
    for i, pdf_file in enumerate(pdf_list, 1):
        print(f"{i:2d}. {pdf_file}")
    
    print()
    print("=" * 50)
    print("使用說明:")
    print("1. 複製上面的PDF文件清單")
    print("2. 執行 MeasurePointParser.py")
    print("3. 選擇 '2. 處理指定的PDF檔案列表'")
    print("4. 貼上PDF文件清單")
    print("5. 重新處理這些文件")
    print("=" * 50)
    
    # 將結果寫入文件
    with open('empty_pdf_files.txt', 'w', encoding='utf-8') as f:
        f.write("需要重新處理的PDF文件清單:\n")
        f.write("=" * 40 + "\n")
        f.write("逗號分隔格式:\n")
        f.write(pdf_files + "\n\n")
        f.write("詳細清單:\n")
        for i, pdf_file in enumerate(pdf_list, 1):
            f.write(f"{i:2d}. {pdf_file}\n")
        f.write("\n使用方式:\n")
        f.write("複製逗號分隔格式的內容，在MeasurePointParser.py中選擇選項2使用\n")
    
    print(f"結果已儲存至 empty_pdf_files.txt")

if __name__ == "__main__":
    main()
