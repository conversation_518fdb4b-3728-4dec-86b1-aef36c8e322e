#!/usr/bin/env python
# coding: utf-8

"""
PCB量測點位解析器 - 批次執行腳本
提供友好的使用者介面來執行批次處理
"""

import os
import sys
from datetime import datetime
from MeasurePointParser import batch_process_pdfs

def check_prerequisites():
    """檢查執行前置條件"""
    print("檢查執行環境...")
    
    # 檢查PDFData資料夾
    if not os.path.exists('PDFData'):
        print("❌ 錯誤: PDFData資料夾不存在")
        print("請建立PDFData資料夾並放入要處理的PDF檔案")
        return False
    
    # 檢查PDF檔案
    import glob
    pdf_files = glob.glob('PDFData/*.pdf')
    if not pdf_files:
        print("❌ 錯誤: PDFData資料夾中沒有PDF檔案")
        print("請在PDFData資料夾中放入要處理的PDF檔案")
        return False
    
    print(f"✓ 找到 {len(pdf_files)} 個PDF檔案")
    
    # 檢查必要的Python套件
    try:
        import pandas
        import requests
        print("✓ 必要套件已安裝")
    except ImportError as e:
        print(f"❌ 錯誤: 缺少必要套件 {e}")
        print("請執行: pip install pandas requests")
        return False
    
    return True

def show_file_list():
    """顯示要處理的檔案清單"""
    import glob
    pdf_files = glob.glob('PDFData/*.pdf')
    
    print("\n要處理的PDF檔案:")
    print("-" * 50)
    for i, file_path in enumerate(pdf_files, 1):
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) / 1024  # KB
        print(f"{i:2d}. {filename} ({file_size:.1f} KB)")
    print("-" * 50)

def main():
    """主程式"""
    print("=" * 60)
    print("PCB量測點位解析器 - 批次處理")
    print("=" * 60)
    print(f"執行時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 檢查前置條件
    if not check_prerequisites():
        print("\n請解決上述問題後重新執行程式")
        input("按Enter鍵退出...")
        return
    
    # 顯示檔案清單
    show_file_list()
    
    # 確認執行
    print("\n注意事項:")
    print("1. 處理過程中請保持網路連線穩定")
    print("2. 程式會自動生成日誌檔案在logs資料夾中")
    print("3. 每個PDF檔案會生成對應的CSV檔案")
    print("4. 處理時間取決於檔案數量和網路速度")
    
    while True:
        choice = input("\n是否開始批次處理? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            break
        elif choice in ['n', 'no', '否']:
            print("已取消執行")
            input("按Enter鍵退出...")
            return
        else:
            print("請輸入 y 或 n")
    
    print("\n" + "=" * 60)
    print("開始批次處理...")
    print("=" * 60)
    
    try:
        # 執行批次處理
        result = batch_process_pdfs()
        
        # 顯示結果
        print("\n" + "=" * 60)
        print("批次處理完成!")
        print("=" * 60)
        print(f"總檔案數: {result['total']}")
        print(f"成功處理: {result['success']}")
        print(f"處理失敗: {result['error']}")
        print(f"成功率: {result['success']/result['total']*100:.1f}%")
        
        if result['error_files']:
            print(f"\n失敗檔案 ({len(result['error_files'])} 個):")
            for error_file in result['error_files']:
                print(f"  - {os.path.basename(error_file)}")
        
        print(f"\n日誌檔案已儲存在 logs 資料夾中")
        print("CSV檔案已儲存在 PDFData 資料夾中")
        
    except KeyboardInterrupt:
        print("\n\n使用者中斷執行")
    except Exception as e:
        print(f"\n\n執行過程中發生錯誤: {str(e)}")
        print("請檢查日誌檔案以獲得更多資訊")
    
    print("\n" + "=" * 60)
    input("按Enter鍵退出...")

if __name__ == "__main__":
    main() 