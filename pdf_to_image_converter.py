#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF轉圖片轉換器
將PDFData資料夾內的PDF檔案轉換為圖片並儲存到OutputImages資料夾
"""

import os
import sys
from pathlib import Path
from pdf2image import convert_from_path
from PIL import Image
import logging

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

class PDFToImageConverter:
    def __init__(self, pdf_folder="PDFData", output_folder="OutputImages"):
        """
        初始化PDF轉圖片轉換器
        
        Args:
            pdf_folder (str): PDF檔案所在資料夾
            output_folder (str): 輸出圖片資料夾
        """
        self.pdf_folder = Path(pdf_folder)
        self.output_folder = Path(output_folder)
        
        # 建立輸出資料夾
        self.output_folder.mkdir(exist_ok=True)
        
        # 支援的圖片格式
        self.image_formats = ['PNG', 'JPEG', 'TIFF']
        
    def convert_single_pdf(self, pdf_path, output_format='PNG', dpi=200):
        """
        轉換單一PDF檔案為圖片
        
        Args:
            pdf_path (Path): PDF檔案路徑
            output_format (str): 輸出圖片格式 (PNG, JPEG, TIFF)
            dpi (int): 圖片解析度
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            logging.info(f"開始轉換: {pdf_path.name}")
            
            # 轉換PDF為圖片
            pages = convert_from_path(
                pdf_path, 
                dpi=dpi,
                fmt=output_format.lower()
            )
            
            # 取得PDF檔名
            pdf_name = pdf_path.stem
            
            # 儲存每一頁，直接輸出到OutputImages目錄
            for i, page in enumerate(pages, 1):
                if output_format.upper() == 'JPEG':
                    # JPEG不支援透明度，需要轉換為RGB
                    if page.mode in ('RGBA', 'LA', 'P'):
                        page = page.convert('RGB')
                
                output_filename = f"{pdf_name}_page_{i:03d}.{output_format.lower()}"
                output_path = self.output_folder / output_filename
                
                page.save(output_path, output_format.upper())
                logging.info(f"已儲存: {output_path}")
            
            logging.info(f"✅ 成功轉換 {pdf_path.name} ({len(pages)} 頁)")
            return True
            
        except Exception as e:
            logging.error(f"❌ 轉換失敗 {pdf_path.name}: {str(e)}")
            return False
    
    def convert_all_pdfs(self, output_format='PNG', dpi=200, max_files=None):
        """
        轉換資料夾內所有PDF檔案
        
        Args:
            output_format (str): 輸出圖片格式
            dpi (int): 圖片解析度
            max_files (int): 最大轉換檔案數量 (None表示全部)
        """
        if not self.pdf_folder.exists():
            logging.error(f"PDF資料夾不存在: {self.pdf_folder}")
            return
        
        # 取得所有PDF檔案
        pdf_files = list(self.pdf_folder.glob("*.pdf"))
        
        if not pdf_files:
            logging.warning("未找到任何PDF檔案")
            return
        
        # 限制處理檔案數量
        if max_files:
            pdf_files = pdf_files[:max_files]
        
        logging.info(f"找到 {len(pdf_files)} 個PDF檔案")
        logging.info(f"輸出格式: {output_format}, DPI: {dpi}")
        
        success_count = 0
        total_count = len(pdf_files)
        
        for i, pdf_file in enumerate(pdf_files, 1):
            logging.info(f"進度: {i}/{total_count}")
            
            if self.convert_single_pdf(pdf_file, output_format, dpi):
                success_count += 1
        
        logging.info(f"轉換完成! 成功: {success_count}/{total_count}")
    
    def get_pdf_info(self):
        """取得PDF檔案資訊"""
        if not self.pdf_folder.exists():
            logging.error(f"PDF資料夾不存在: {self.pdf_folder}")
            return
        
        pdf_files = list(self.pdf_folder.glob("*.pdf"))
        
        logging.info(f"PDF資料夾: {self.pdf_folder}")
        logging.info(f"找到 {len(pdf_files)} 個PDF檔案")
        
        for pdf_file in pdf_files[:10]:  # 只顯示前10個
            size_mb = pdf_file.stat().st_size / (1024 * 1024)
            logging.info(f"  - {pdf_file.name} ({size_mb:.2f} MB)")
        
        if len(pdf_files) > 10:
            logging.info(f"  ... 還有 {len(pdf_files) - 10} 個檔案")

def main():
    """主程式"""
    print("=== PDF轉圖片轉換器 ===")
    
    # 建立轉換器
    converter = PDFToImageConverter()
    
    # 顯示PDF檔案資訊
    converter.get_pdf_info()
    
    print("\n請選擇操作:")
    print("1. 轉換所有PDF為PNG (推薦)")
    print("2. 轉換所有PDF為JPEG")
    print("3. 轉換前5個PDF (測試)")
    print("4. 自訂設定")
    print("0. 退出")
    
    try:
        choice = input("\n請輸入選項 (0-4): ").strip()
        
        if choice == '0':
            print("程式結束")
            return
        elif choice == '1':
            converter.convert_all_pdfs(output_format='PNG', dpi=200)
        elif choice == '2':
            converter.convert_all_pdfs(output_format='JPEG', dpi=200)
        elif choice == '3':
            converter.convert_all_pdfs(output_format='PNG', dpi=200, max_files=5)
        elif choice == '4':
            # 自訂設定
            format_choice = input("輸出格式 (PNG/JPEG/TIFF) [PNG]: ").strip().upper() or 'PNG'
            if format_choice not in ['PNG', 'JPEG', 'TIFF']:
                format_choice = 'PNG'
            
            dpi_input = input("DPI解析度 (100-300) [200]: ").strip()
            try:
                dpi = int(dpi_input) if dpi_input else 200
                dpi = max(100, min(300, dpi))  # 限制在100-300之間
            except ValueError:
                dpi = 200
            
            max_files_input = input("最大檔案數量 (留空表示全部): ").strip()
            max_files = int(max_files_input) if max_files_input.isdigit() else None
            
            converter.convert_all_pdfs(output_format=format_choice, dpi=dpi, max_files=max_files)
        else:
            print("無效選項")
            
    except KeyboardInterrupt:
        print("\n程式被中斷")
    except Exception as e:
        logging.error(f"程式執行錯誤: {str(e)}")

if __name__ == "__main__":
    main() 