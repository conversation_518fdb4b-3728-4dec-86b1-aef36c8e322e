import requests
import base64
import json 
import os
import glob
import pandas as pd
import io

def encode_file(file_path):
    """將檔案轉換為 base64 編碼"""
    with open(file_path, 'rb') as file:
        return base64.b64encode(file.read()).decode('utf-8')

def upload_files_with_message(file_paths, message="", model = 'gemini-2.0-flash-001'):
    """上傳多個檔案和文字訊息到伺服器"""
    url = 'http://172.16.2.25:5000/multimodalapi'
    
    # 準備檔案資料
    files_data = []
    for file_path in file_paths:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            content = encode_file(file_path)
            files_data.append({
                'filename': filename,
                'content': content
            })
    
    # 準備要傳送的資料
    payload = {
        'files': files_data,
        'message': message,
        'model': model
    }
    
    # 發送請求
    try:
        response = requests.post(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        return response
        # 輸出結果
        print(f'Status Code: {response.status_code}')
        # print('Response:', json.dumps(response.json(), indent=2))
        print(response.text)
        
    except Exception as e:
        print(f'Error: {str(e)}')
        return f'Error: {str(e)}'

def parse_csv_response(csv_text):
    """解析API回傳的CSV格式資料"""
    try:
        # 將CSV文字轉換為DataFrame
        csv_io = io.StringIO(csv_text)
        df = pd.read_csv(csv_io)
        
        print("=== 解析結果 ===")
        print(f"找到 {len(df)} 筆資料")
        print(f"欄位: {list(df.columns)}")
        print("\nDataFrame:")
        print(df)
        
        # 統計分析
        if 'MARK' in df.columns:
            print(f"\n=== MARK統計 ===")
            mark_counts = df['MARK'].value_counts()
            print(mark_counts)
        
        if '層別' in df.columns:
            print(f"\n=== 層別統計 ===")
            layer_counts = df['層別'].value_counts()
            print(layer_counts)
        
        # 轉換為字典列表方便後續處理
        records = df.to_dict('records')
        print(f"\n=== 詳細資料 ===")
        for i, record in enumerate(records, 1):
            print(f"第{i}筆: {record}")
        
        return df, records
        
    except Exception as e:
        print(f"CSV解析錯誤: {str(e)}")
        print("原始回傳內容:")
        print(csv_text)
        return None, None

files = glob.glob('OutputImages/*.png')
message = """(1.)解析表格中C面與S面有哪些符號(A、B、C、D......)，精確地搜尋各個圖片角落不可有遺漏符號
(2.)統整哪些符號存在可能於(C面、S面、或是C面&S面)
(3.)將結果輸出成CSV型式，方便以python pandas讀取，不須有其他的解釋
(4.)欄位順序呈現(1)MARK(2)層別(C面、S面.....)，MARK與層別(各欄位則一)需獨力輸出欄位結果，如有兩個層別請分開輸出
(5.)如圖片中有太小或太過於糊的符號請先放大增加解析度再解析結果
(6.)如搜尋不到符號輸出無，並輸出圖片位於的層別處(C面、S面)，精確呈現有圖片在的層別即可"""
files_to_upload = [
    files[0],
]
response = upload_files_with_message(files_to_upload, message)

# 取得API回傳的內容
api_response = response.json()['response']
print("=== API原始回傳 ===")
print(api_response)

# 解析CSV格式的回傳資料
print("\n" + "="*50)
df, records = parse_csv_response(api_response)

# 如果解析成功，可以進行後續處理
if df is not None:
    # 儲存為CSV檔案
    output_file = 'parsed_results.csv'
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n✅ 結果已儲存至: {output_file}")
    
    # 示例：篩選特定條件的資料
    if 'MARK' in df.columns and '層別' in df.columns:
        print("\n=== 資料篩選示例 ===")
        
        # 篩選C面的資料
        c_face_data = df[df['層別'] == 'C面']
        if not c_face_data.empty:
            print("C面的符號:")
            print(c_face_data['MARK'].tolist())
        
        # 篩選S面的資料
        s_face_data = df[df['層別'] == 'S面']
        if not s_face_data.empty:
            print("S面的符號:")
            print(s_face_data['MARK'].tolist())
        
        # 篩選非"無"的符號
        valid_marks = df[df['MARK'] != '無']
        if not valid_marks.empty:
            print("找到的有效符號:")
            print(valid_marks[['MARK', '層別']].to_string(index=False))